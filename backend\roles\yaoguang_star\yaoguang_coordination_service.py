"""
瑶光星协调服务
基于新的协调职责重构的瑶光星主服务架构
"""

import asyncio
import logging
from datetime import datetime, timedelta
from typing import Dict, List, Any, Optional
from dataclasses import dataclass

# 导入核心协调引擎
from .core.seven_star_coordinator import seven_star_coordinator
from .core.learning_flow_coordinator import learning_flow_coordinator
from .core.backtest_flow_coordinator import backtest_flow_coordinator, BacktestConfig
from .core.feedback_distribution_engine import feedback_distribution_engine

logger = logging.getLogger(__name__)

@dataclass
class CoordinationRequest:
    """协调请求"""
    request_id: str
    request_type: str  # 'learning', 'backtest', 'feedback'
    priority: str      # 'high', 'medium', 'low'
    config: Dict[str, Any]
    requester: str
    created_time: datetime

class YaoguangCoordinationService:
    """瑶光星协调服务"""
    
    def __init__(self):
        self.service_name = "瑶光星协调服务"
        self.version = "2.0.0"
        self.service_type = "coordination_hub"
        
        # 协调引擎实例
        self.seven_star_coordinator = seven_star_coordinator
        self.learning_coordinator = learning_flow_coordinator
        self.backtest_coordinator = backtest_flow_coordinator
        self.feedback_engine = feedback_distribution_engine
        
        # 服务状态
        self.service_status = {
            "is_active": True,
            "start_time": datetime.now(),
            "coordination_sessions": {
                "learning": 0,
                "backtest": 0,
                "feedback": 0
            },
            "performance_metrics": {
                "total_coordinations": 0,
                "successful_coordinations": 0,
                "average_coordination_time": 0.0,
                "star_collaboration_score": 0.0
            }
        }
        
        # 协调配置
        self.coordination_config = {
            "max_concurrent_coordinations": 5,
            "enable_auto_feedback": True,
            "enable_cross_session_learning": True,
            "coordination_timeout": 3600,  # 1小时
            "feedback_delay": 1,  # 1秒后自动反馈
            "performance_monitoring": True
        }
        
        # 请求队列
        self.pending_requests: List[CoordinationRequest] = []
        self.active_coordinations: Dict[str, Dict[str, Any]] = {}
        self.completed_coordinations: List[Dict[str, Any]] = []
        
        logger.info(f"✅ {self.service_name} v{self.version} 初始化完成")
    
    async def coordinate_learning_session(self, learning_config: Dict[str, Any]) -> Dict[str, Any]:
        """协调学习会话"""
        try:
            request_id = f"learning_coord_{datetime.now().strftime('%Y%m%d_%H%M%S')}"
            
            logger.info(f"🎓 开始协调学习会话: {request_id}")
            
            # 创建协调请求
            request = CoordinationRequest(
                request_id=request_id,
                request_type="learning",
                priority=learning_config.get("priority", "medium"),
                config=learning_config,
                requester=learning_config.get("requester", "system"),
                created_time=datetime.now()
            )
            
            # 检查并发限制
            if len(self.active_coordinations) >= self.coordination_config["max_concurrent_coordinations"]:
                self.pending_requests.append(request)
                return {
                    "success": False,
                    "request_id": request_id,
                    "status": "queued",
                    "message": "已加入等待队列，当前协调会话已满"
                }
            
            # 启动协调流程
            coordination_result = await self._execute_learning_coordination(request)
            
            # 更新服务状态
            self.service_status["coordination_sessions"]["learning"] += 1
            self.service_status["performance_metrics"]["total_coordinations"] += 1
            
            if coordination_result.get("success"):
                self.service_status["performance_metrics"]["successful_coordinations"] += 1
                
                # 自动触发反馈分发（异步执行，不等待）
                if self.coordination_config["enable_auto_feedback"]:
                    asyncio.create_task(self._schedule_auto_feedback(request_id, coordination_result, "learning"))
            
            return coordination_result
            
        except Exception as e:
            logger.error(f"协调学习会话失败: {e}")
            return {
                "success": False,
                "error": str(e),
                "request_id": request_id if 'request_id' in locals() else "unknown"
            }
    
    async def coordinate_backtest_session(self, backtest_config: Dict[str, Any]) -> Dict[str, Any]:
        """协调回测会话"""
        try:
            request_id = f"backtest_coord_{datetime.now().strftime('%Y%m%d_%H%M%S')}"
            
            logger.info(f"🔄 开始协调回测会话: {request_id}")
            
            # 创建协调请求
            request = CoordinationRequest(
                request_id=request_id,
                request_type="backtest",
                priority=backtest_config.get("priority", "medium"),
                config=backtest_config,
                requester=backtest_config.get("requester", "system"),
                created_time=datetime.now()
            )
            
            # 检查并发限制
            if len(self.active_coordinations) >= self.coordination_config["max_concurrent_coordinations"]:
                self.pending_requests.append(request)
                return {
                    "success": False,
                    "request_id": request_id,
                    "status": "queued",
                    "message": "已加入等待队列，当前协调会话已满"
                }
            
            # 启动协调流程
            coordination_result = await self._execute_backtest_coordination(request)
            
            # 更新服务状态
            self.service_status["coordination_sessions"]["backtest"] += 1
            self.service_status["performance_metrics"]["total_coordinations"] += 1
            
            if coordination_result.get("success"):
                self.service_status["performance_metrics"]["successful_coordinations"] += 1
                
                # 自动触发反馈分发（异步执行，不等待）
                if self.coordination_config["enable_auto_feedback"]:
                    asyncio.create_task(self._schedule_auto_feedback(request_id, coordination_result, "backtest"))
            
            return coordination_result
            
        except Exception as e:
            logger.error(f"协调回测会话失败: {e}")
            return {
                "success": False,
                "error": str(e),
                "request_id": request_id if 'request_id' in locals() else "unknown"
            }
    
    async def distribute_feedback(self, feedback_config: Dict[str, Any]) -> Dict[str, Any]:
        """分发反馈"""
        try:
            request_id = f"feedback_coord_{datetime.now().strftime('%Y%m%d_%H%M%S')}"
            
            logger.info(f"📤 开始分发反馈: {request_id}")
            
            feedback_type = feedback_config.get("feedback_type", "learning")
            source_data = feedback_config.get("source_data", {})
            
            if feedback_type == "learning":
                result = await self.feedback_engine.distribute_learning_feedback(source_data)
            elif feedback_type == "backtest":
                result = await self.feedback_engine.distribute_backtest_feedback(source_data)
            else:
                return {
                    "success": False,
                    "error": f"不支持的反馈类型: {feedback_type}",
                    "request_id": request_id
                }
            
            # 更新服务状态
            self.service_status["coordination_sessions"]["feedback"] += 1
            
            return {
                "success": result.get("success", False),
                "request_id": request_id,
                "feedback_result": result,
                "feedback_type": feedback_type
            }
            
        except Exception as e:
            logger.error(f"分发反馈失败: {e}")
            return {
                "success": False,
                "error": str(e),
                "request_id": request_id if 'request_id' in locals() else "unknown"
            }
    
    async def _execute_learning_coordination(self, request: CoordinationRequest) -> Dict[str, Any]:
        """执行学习协调"""
        try:
            # 记录开始时间
            start_time = datetime.now()
            self.active_coordinations[request.request_id] = {
                "request": request,
                "start_time": start_time,
                "status": "running"
            }
            
            # 使用七星协调器或学习流程协调器
            if request.config.get("use_detailed_flow", False):
                # 使用详细的学习流程协调器
                result = await self.learning_coordinator.start_comprehensive_learning(request.config)
            else:
                # 使用七星协调器
                result = await self.seven_star_coordinator.start_learning_coordination(request.config)
            
            # 计算执行时间
            execution_time = (datetime.now() - start_time).total_seconds()
            
            # 移动到已完成列表
            coordination_record = self.active_coordinations.pop(request.request_id)
            coordination_record.update({
                "end_time": datetime.now(),
                "execution_time": execution_time,
                "result": result,
                "status": "completed" if result.get("success") else "failed"
            })
            self.completed_coordinations.append(coordination_record)
            
            return {
                "success": result.get("success", False),
                "request_id": request.request_id,
                "coordination_result": result,
                "execution_time": execution_time,
                "coordinator_used": "learning_flow" if request.config.get("use_detailed_flow") else "seven_star"
            }
            
        except Exception as e:
            # 清理活跃协调记录
            if request.request_id in self.active_coordinations:
                del self.active_coordinations[request.request_id]
            
            logger.error(f"执行学习协调失败: {e}")
            return {
                "success": False,
                "error": str(e),
                "request_id": request.request_id
            }
    
    async def _execute_backtest_coordination(self, request: CoordinationRequest) -> Dict[str, Any]:
        """执行回测协调"""
        try:
            # 记录开始时间
            start_time = datetime.now()
            self.active_coordinations[request.request_id] = {
                "request": request,
                "start_time": start_time,
                "status": "running"
            }
            
            # 创建回测配置对象
            backtest_config = BacktestConfig(
                backtest_id=request.request_id,
                strategy_name=request.config.get("strategy_name", "默认策略"),
                start_date=request.config.get("start_date", "2024-01-01"),
                end_date=request.config.get("end_date", "2024-12-31"),
                initial_capital=request.config.get("initial_capital", 1000000.0),
                benchmark=request.config.get("benchmark", "000300.XSHG"),
                universe_size=request.config.get("universe_size", 100),
                rebalance_frequency=request.config.get("rebalance_frequency", "monthly"),
                commission_rate=request.config.get("commission_rate", 0.0003),
                slippage_rate=request.config.get("slippage_rate", 0.001),
                max_position_size=request.config.get("max_position_size", 0.1)
            )
            
            # 使用回测流程协调器
            result = await self.backtest_coordinator.start_comprehensive_backtest(backtest_config)
            
            # 计算执行时间
            execution_time = (datetime.now() - start_time).total_seconds()
            
            # 移动到已完成列表
            coordination_record = self.active_coordinations.pop(request.request_id)
            coordination_record.update({
                "end_time": datetime.now(),
                "execution_time": execution_time,
                "result": result,
                "status": "completed" if result.get("success") else "failed"
            })
            self.completed_coordinations.append(coordination_record)
            
            return {
                "success": result.get("success", False),
                "request_id": request.request_id,
                "coordination_result": result,
                "execution_time": execution_time,
                "coordinator_used": "backtest_flow"
            }
            
        except Exception as e:
            # 清理活跃协调记录
            if request.request_id in self.active_coordinations:
                del self.active_coordinations[request.request_id]
            
            logger.error(f"执行回测协调失败: {e}")
            return {
                "success": False,
                "error": str(e),
                "request_id": request.request_id
            }

    async def _schedule_auto_feedback(self, request_id: str, coordination_result: Dict[str, Any], session_type: str):
        """安排自动反馈"""
        try:
            # 延迟一段时间后自动分发反馈
            await asyncio.sleep(self.coordination_config["feedback_delay"])

            logger.info(f"🔄 自动分发反馈: {request_id}")

            feedback_config = {
                "feedback_type": session_type,
                "source_data": {
                    "session_id": request_id,
                    "coordination_result": coordination_result,
                    "session_type": session_type
                }
            }

            feedback_result = await self.distribute_feedback(feedback_config)

            if feedback_result.get("success"):
                logger.info(f"✅ 自动反馈分发成功: {request_id}")
            else:
                logger.warning(f"⚠️ 自动反馈分发失败: {request_id}")

        except Exception as e:
            logger.error(f"安排自动反馈失败: {e}")

    async def get_coordination_status(self) -> Dict[str, Any]:
        """获取协调状态"""
        try:
            # 计算平均协调时间
            if self.completed_coordinations:
                total_time = sum(coord.get("execution_time", 0) for coord in self.completed_coordinations)
                avg_time = total_time / len(self.completed_coordinations)
                self.service_status["performance_metrics"]["average_coordination_time"] = avg_time

            # 计算星际协作得分
            collaboration_score = self._calculate_collaboration_score()
            self.service_status["performance_metrics"]["star_collaboration_score"] = collaboration_score

            return {
                "service_info": {
                    "service_name": self.service_name,
                    "version": self.version,
                    "service_type": self.service_type,
                    "uptime": str(datetime.now() - self.service_status["start_time"])
                },
                "coordination_status": {
                    "active_coordinations": len(self.active_coordinations),
                    "pending_requests": len(self.pending_requests),
                    "completed_coordinations": len(self.completed_coordinations)
                },
                "session_statistics": self.service_status["coordination_sessions"],
                "performance_metrics": self.service_status["performance_metrics"],
                "engine_status": {
                    "seven_star_coordinator": "active",
                    "learning_coordinator": "active",
                    "backtest_coordinator": "active",
                    "feedback_engine": "active"
                }
            }

        except Exception as e:
            logger.error(f"获取协调状态失败: {e}")
            return {
                "error": str(e),
                "timestamp": datetime.now().isoformat()
            }

    def _calculate_collaboration_score(self) -> float:
        """计算星际协作得分"""
        if not self.completed_coordinations:
            return 0.0

        # 基于成功率和执行时间计算协作得分
        successful_coords = [coord for coord in self.completed_coordinations if coord.get("result", {}).get("success")]
        success_rate = len(successful_coords) / len(self.completed_coordinations)

        # 基于执行时间的一致性计算协作效率
        execution_times = [coord.get("execution_time", 0) for coord in successful_coords]
        if len(execution_times) > 1:
            import statistics
            time_consistency = 1.0 - min(1.0, statistics.stdev(execution_times) / max(1, statistics.mean(execution_times)))
            collaboration_score = (success_rate * 0.7 + time_consistency * 0.3)
        else:
            collaboration_score = success_rate

        return min(1.0, collaboration_score)

    async def process_pending_requests(self):
        """处理待处理请求"""
        try:
            while (self.pending_requests and
                   len(self.active_coordinations) < self.coordination_config["max_concurrent_coordinations"]):

                request = self.pending_requests.pop(0)

                logger.info(f"🔄 处理待处理请求: {request.request_id}")

                if request.request_type == "learning":
                    result = await self._execute_learning_coordination(request)
                elif request.request_type == "backtest":
                    result = await self._execute_backtest_coordination(request)
                else:
                    logger.warning(f"未知请求类型: {request.request_type}")
                    continue

                # 更新统计
                self.service_status["coordination_sessions"][request.request_type] += 1
                self.service_status["performance_metrics"]["total_coordinations"] += 1

                if result.get("success"):
                    self.service_status["performance_metrics"]["successful_coordinations"] += 1

                    # 自动触发反馈分发
                    if self.coordination_config["enable_auto_feedback"]:
                        await self._schedule_auto_feedback(request.request_id, result, request.request_type)

        except Exception as e:
            logger.error(f"处理待处理请求失败: {e}")

    async def get_coordination_history(self, limit: int = 10) -> List[Dict[str, Any]]:
        """获取协调历史"""
        try:
            # 按完成时间排序，最新的在前
            sorted_coordinations = sorted(
                self.completed_coordinations,
                key=lambda x: x.get("end_time", datetime.min),
                reverse=True
            )

            history = []
            for coord in sorted_coordinations[:limit]:
                request = coord.get("request")
                history.append({
                    "request_id": request.request_id if request else "unknown",
                    "request_type": request.request_type if request else "unknown",
                    "priority": request.priority if request else "unknown",
                    "requester": request.requester if request else "unknown",
                    "start_time": coord.get("start_time", datetime.min).isoformat(),
                    "end_time": coord.get("end_time", datetime.min).isoformat(),
                    "execution_time": coord.get("execution_time", 0),
                    "status": coord.get("status", "unknown"),
                    "success": coord.get("result", {}).get("success", False)
                })

            return history

        except Exception as e:
            logger.error(f"获取协调历史失败: {e}")
            return []

    async def shutdown_service(self):
        """关闭服务"""
        try:
            logger.info(f"🔄 正在关闭 {self.service_name}...")

            # 等待活跃协调完成
            while self.active_coordinations:
                logger.info(f"等待 {len(self.active_coordinations)} 个活跃协调完成...")
                await asyncio.sleep(1)

            # 更新服务状态
            self.service_status["is_active"] = False

            logger.info(f"✅ {self.service_name} 已成功关闭")

            return {
                "success": True,
                "message": "服务已成功关闭",
                "final_statistics": {
                    "total_coordinations": self.service_status["performance_metrics"]["total_coordinations"],
                    "successful_coordinations": self.service_status["performance_metrics"]["successful_coordinations"],
                    "uptime": str(datetime.now() - self.service_status["start_time"])
                }
            }

        except Exception as e:
            logger.error(f"关闭服务失败: {e}")
            return {
                "success": False,
                "error": str(e)
            }

# 全局实例
yaoguang_coordination_service = YaoguangCoordinationService()
