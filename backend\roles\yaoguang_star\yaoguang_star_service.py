#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
瑶光星智能体服务
基于通用智能体框架
"""

import asyncio
import logging
from datetime import datetime
from typing import Dict, List, Any

# 检查超参数优化服务可用性
try:
    from .services.hyperparameter_optimization_service import YaoguangHyperparameterOptimizationService
    HYPERPARAMETER_OPTIMIZATION_AVAILABLE = True
except ImportError as e:
    logger.warning(f"超参数优化服务导入失败: {e}")
    HYPERPARAMETER_OPTIMIZATION_AVAILABLE = False

logger = logging.getLogger(__name__)

class YaoguangStarService:
    """瑶光星智能体服务"""
    
    def __init__(self):
        self.service_name = "瑶光星智能体"
        self.version = "2.0.0"
        self.star_key = "yaoguang"
        self.autonomous_mode = False
        self.intelligence_level = "advanced"
        
        # 通用智能体框架
        self.universal_agent = None
        self.universal_framework = None
        self.collaboration_system = None
        self._initialize_universal_agent()

        # 超参数优化服务
        self.hyperparameter_optimization_service = None
        self._initialize_hyperparameter_optimization_service()

        # 数据管理服务
        self.data_management_service = None
        self._initialize_data_management_service()

        logger.info(f"✅ {self.service_name} v{self.version} 初始化完成")

    async def run_backtest(self, context: Dict[str, Any] = None) -> Dict[str, Any]:
        """回测 - 瑶光星核心方法"""
        try:
            logger.info(f"📈 瑶光星开始回测")

            # 获取回测参数
            strategy = context.get("strategy", {}) if context else {}
            start_date = context.get("start_date", "2023-01-01") if context else "2023-01-01"
            end_date = context.get("end_date", "2024-01-01") if context else "2024-01-01"
            initial_capital = context.get("initial_capital", 1000000) if context else 1000000

            # 使用RD-Agent回测服务
            if hasattr(self, 'rd_agent_integration_service') and self.rd_agent_integration_service:
                try:
                    backtest_result = await self.rd_agent_integration_service.run_backtest(
                        strategy, start_date, end_date, initial_capital
                    )

                    if backtest_result.get("success"):
                        logger.info(f"✅ 瑶光星RD-Agent回测完成")
                        return backtest_result

                except Exception as e:
                    logger.warning(f"RD-Agent回测失败，使用基础回测: {e}")

            # 基础回测模拟
            backtest_results = {
                "strategy_name": strategy.get("name", "默认策略"),
                "backtest_period": f"{start_date} 到 {end_date}",
                "initial_capital": initial_capital,
                "final_capital": initial_capital * 1.15,  # 模拟15%收益
                "total_return": 0.15,
                "annual_return": 0.12,
                "max_drawdown": 0.08,
                "sharpe_ratio": 1.2,
                "win_rate": 0.65,
                "total_trades": 120,
                "winning_trades": 78,
                "losing_trades": 42,
                "performance_metrics": {
                    "volatility": 0.18,
                    "beta": 1.05,
                    "alpha": 0.03,
                    "information_ratio": 0.8
                },
                "monthly_returns": [
                    0.02, 0.01, -0.01, 0.03, 0.02, 0.01,
                    0.02, -0.01, 0.03, 0.01, 0.02, 0.01
                ]
            }

            return {
                "success": True,
                "backtest_results": backtest_results,
                "analysis_type": "comprehensive",
                "timestamp": datetime.now().isoformat()
            }

        except Exception as e:
            logger.error(f"回测失败: {e}")
            return {
                "success": False,
                "error": str(e),
                "timestamp": datetime.now().isoformat()
            }

    def _initialize_universal_agent_sync(self):
        """同步初始化通用智能体 - 使用统一初始化器消除重复代码"""
        try:
            from shared.intelligence.universal_agent_initializer import universal_agent_initializer

            # 使用统一初始化器的同步方法
            initialization_result = universal_agent_initializer.initialize_agent_sync(
                "瑶光星", self
            )

            # 记录初始化结果
            self.agent_initialization_result = initialization_result

            if initialization_result.get("status") == "success":
                logger.info(f"🧠 {self.service_name} 智能体框架同步初始化完成: {initialization_result.get('successful_components', 0)}/{initialization_result.get('total_components', 0)} 组件")
                self.intelligence_level = "advanced"
            else:
                logger.error(f"❌ {self.service_name} 智能体框架同步初始化失败")
                self.intelligence_level = "basic"

        except Exception as e:
            logger.error(f"通用智能体同步初始化失败: {e}")
            self.intelligence_level = "basic"

    async def _initialize_universal_agent(self):
        """异步初始化通用智能体 - 保留异步接口"""
        try:
            from shared.intelligence.universal_agent_initializer import universal_agent_initializer

            # 使用统一初始化器
            initialization_result = await universal_agent_initializer.initialize_complete_agent_framework(
                "瑶光星", self
            )

            # 记录初始化结果
            self.agent_initialization_result = initialization_result

            if initialization_result.get("status") in ["success", "partial_success"]:
                logger.info(f"🧠 {self.service_name} 智能体框架异步初始化完成: {initialization_result.get('successful_components', 0)}/{initialization_result.get('total_components', 0)} 组件")
                self.intelligence_level = "advanced"
            else:
                logger.error(f"❌ {self.service_name} 智能体框架异步初始化失败")
                self.intelligence_level = "basic"

        except Exception as e:
            logger.error(f"通用智能体异步初始化失败: {e}")
            self.intelligence_level = "basic"

    def _initialize_hyperparameter_optimization_service(self):
        """初始化超参数优化服务"""
        try:
            if HYPERPARAMETER_OPTIMIZATION_AVAILABLE:
                self.hyperparameter_optimization_service = YaoguangHyperparameterOptimizationService()
                logger.info(f"🔧 {self.service_name} 超参数优化服务初始化完成")
            else:
                logger.warning(f"⚠️ {self.service_name} 超参数优化服务不可用")
                self.hyperparameter_optimization_service = None
        except Exception as e:
            logger.error(f"超参数优化服务初始化失败: {e}")
            self.hyperparameter_optimization_service = None

    def _initialize_data_management_service(self):
        """数据管理服务已移除 - 瑶光星使用真实数据管理器"""
        # 瑶光星专注于学习和回测，数据获取由开阳星负责
        self.data_management_service = None
        logger.info(f"📊 {self.service_name} 使用真实数据管理器进行数据访问")

    # 实时数据获取功能已移除 - 应由开阳星负责
    # 瑶光星专注于学习和回测功能

    # 瑶光星模拟数据方法已删除 - 不再使用模拟数据

    async def get_stock_data(self, stock_code: str, start_date: str, end_date: str) -> Dict[str, Any]:
        """获取股票数据"""
        try:
            # 直接使用基础数据获取（确保可用）
            return await self._get_basic_stock_data(stock_code, start_date, end_date)

        except Exception as e:
            logger.error(f"获取股票数据失败: {e}")
            return {"success": False, "error": str(e)}

    async def _get_basic_stock_data(self, stock_code: str, start_date: str, end_date: str) -> Dict[str, Any]:
        """基础股票数据获取"""
        try:
            # 尝试从本地数据库获取
            import pandas as pd
            import numpy as np
            from datetime import datetime, timedelta

            # 生成日期范围
            start_dt = datetime.strptime(start_date, '%Y-%m-%d')
            end_dt = datetime.strptime(end_date, '%Y-%m-%d')
            dates = pd.date_range(start=start_dt, end=end_dt, freq='D')

            # 过滤工作日
            business_days = dates[dates.dayofweek < 5]

            if len(business_days) == 0:
                return {"success": False, "error": "指定日期范围内没有交易日"}

            # 使用真实数据管理器获取数据
            from .core.real_data_manager import real_data_manager

            try:
                # 尝试获取真实历史数据
                real_data = await real_data_manager.get_real_stock_data(stock_code, start_date, end_date)

                if not real_data.empty:
                    # 重命名列以匹配期望格式
                    data = real_data.rename(columns={
                        'open_price': 'open',
                        'high_price': 'high',
                        'low_price': 'low',
                        'close_price': 'close'
                    })

                    # 确保包含必要的列
                    required_columns = ['open', 'high', 'low', 'close', 'volume']
                    for col in required_columns:
                        if col not in data.columns:
                            if col == 'volume' and 'volume' not in data.columns:
                                data['volume'] = 1000000  # 默认成交量
                            else:
                                data[col] = data.get('close', 10.0)  # 使用收盘价填充

                    logger.info(f"成功获取{stock_code}的真实数据，共{len(data)}条记录")
                    return {"success": True, "data": data, "data_source": "real_database"}

            except Exception as e:
                logger.warning(f"获取真实数据失败: {e}")

            # 如果无法获取真实数据，返回错误而不是生成假数据
            logger.error(f"无法获取股票{stock_code}的真实数据，请检查数据库")
            return {
                "success": False,
                "error": f"无法获取股票{stock_code}的真实数据",
                "suggestion": "请确保数据库中有该股票的历史数据"
            }

            logger.info(f"成功生成{stock_code}的基础数据，共{len(data)}条记录")
            return {"success": True, "data": data}

        except Exception as e:
            logger.error(f"基础股票数据获取失败: {e}")
            return {"success": False, "error": str(e)}
    
    async def start_autonomous_mode(self):
        """启动自主模式"""
        if self.autonomous_mode:
            return
        
        self.autonomous_mode = True
        logger.info(f"🚀 启动 {self.service_name} 自主模式")
        
        if self.universal_agent:
            asyncio.create_task(self.universal_agent.start_agent())
    
    async def stop_autonomous_mode(self):
        """停止自主模式"""
        self.autonomous_mode = False
        
        if self.universal_agent:
            await self.universal_agent.stop_agent()
        
        logger.info(f"⏹️ {self.service_name} 自主模式已停止")
    
    async def intelligent_analysis(self, input_data: Dict[str, Any], 
                                 analysis_type: str = "general_analysis") -> Dict[str, Any]:
        """智能分析"""
        try:
            if not self.universal_agent:
                return {"error": "通用智能体框架未初始化"}
            
            logger.info(f"🧠 {self.service_name} 开始智能分析: {analysis_type}")
            
            analysis_result = await self.universal_agent.intelligent_analysis(
                input_data, analysis_type
            )
            
            # 添加专业处理
            specialized_result = analysis_result.copy()
            specialized_result["specialized_insights"] = {
                "star_perspective": "瑶光星专业视角",
                "professional_focus": "yaoguang_analysis"
            }
            
            return {
                "success": True,
                "service": self.service_name,
                "analysis_result": specialized_result,
                "framework_version": "universal_v2.0"
            }
            
        except Exception as e:
            logger.error(f"{self.service_name} 智能分析失败: {e}")
            return {"error": str(e)}
    
    async def collaborative_request(self, target_agents: List[str], 
                                  request_data: Dict[str, Any]) -> Dict[str, Any]:
        """协作请求"""
        try:
            if not self.universal_agent:
                return {"error": "通用智能体框架未初始化"}
            
            logger.info(f"🤝 {self.service_name} 发起协作: {target_agents}")
            
            collaboration_result = await self.universal_agent.collaborative_request(
                target_agents, "collaboration_request", request_data
            )
            
            return {
                "success": True,
                "collaboration_result": collaboration_result
            }
            
        except Exception as e:
            logger.error(f"协作请求失败: {e}")
            return {"error": str(e)}
    
    async def adaptive_learning(self, feedback: Dict[str, Any]) -> Dict[str, Any]:
        """自适应学习"""
        try:
            if not self.universal_agent:
                return {"error": "通用智能体框架未初始化"}
            
            logger.info(f"📚 {self.service_name} 自适应学习")
            
            learning_success = await self.universal_agent.adaptive_learning_from_feedback(feedback)
            
            return {
                "success": learning_success,
                "learning_completed": True,
                "service": self.service_name
            }
            
        except Exception as e:
            logger.error(f"自适应学习失败: {e}")
            return {"error": str(e)}
    
    async def get_agent_status(self) -> Dict[str, Any]:
        """获取智能体状态"""
        try:
            # 基础状态信息
            status = {
                "service_name": self.service_name,
                "version": self.version,
                "star_key": self.star_key,
                "autonomous_mode": self.autonomous_mode,
                "intelligence_level": self.intelligence_level,
                "timestamp": datetime.now().isoformat()
            }

            # 添加瑶光星专用状态
            status.update({
                "learning_algorithms": len(getattr(self, 'learning_algorithms', [])),
                "data_sources": len(getattr(self, 'data_sources', [])),
                "research_areas": len(getattr(self, 'research_areas', [])),
                "current_learning_focus": getattr(self, 'current_learning_focus', "未设置"),
                "active_research": getattr(self, 'active_research', False),
                "data_quality_threshold": getattr(self, 'data_quality_threshold', 0.95),
                "learning_efficiency_target": getattr(self, 'learning_efficiency_target', 0.85)
            })

            # 添加组件状态
            status["components"] = {
                "hyperparameter_optimization_service": self.hyperparameter_optimization_service is not None,
                "data_management_service": self.data_management_service is not None,
                "universal_agent": self.universal_agent is not None,
                "universal_framework": self.universal_framework is not None,
                "collaboration_system": self.collaboration_system is not None
            }

            # 添加核心系统状态
            try:
                from .core.core_systems_integration import yaoguang_core_systems
                if yaoguang_core_systems.is_healthy():
                    core_status = yaoguang_core_systems.get_integration_status()
                    status["core_systems"] = core_status
            except Exception as e:
                status["core_systems"] = {"error": str(e)}

            # 添加通用智能体框架状态
            if self.universal_agent:
                universal_status = await self.universal_agent.get_agent_status()
                status["universal_framework"] = universal_status

            # 添加学习进度状态
            status["learning_progress"] = await self._get_learning_progress()

            # 添加性能监控状态
            status["performance_metrics"] = await self._get_performance_metrics()

            return status

        except Exception as e:
            logger.error(f"获取智能体状态失败: {e}")
            return {"error": str(e)}

    async def _get_learning_progress(self) -> Dict[str, Any]:
        """获取学习进度状态"""
        try:
            # 从数据持久化系统获取学习统计
            from .core.data_persistence import yaoguang_persistence

            # 获取总体统计
            stats = await yaoguang_persistence.get_overall_statistics()

            learning_progress = {
                "total_learning_sessions": stats.get("total_statistics", {}).get("learning_total", 0),
                "today_learning_sessions": stats.get("daily_statistics", {}).get("learning_today", 0),
                "total_factor_research": stats.get("total_statistics", {}).get("factor_research_total", 0),
                "today_factor_research": stats.get("daily_statistics", {}).get("factor_research_today", 0),
                "total_practice_sessions": stats.get("total_statistics", {}).get("practice_total", 0),
                "today_practice_sessions": stats.get("daily_statistics", {}).get("practice_today", 0),
                "data_quality_checks": stats.get("total_statistics", {}).get("data_quality_total", 0),
                "learning_efficiency": 0.85,  # 可以从实际计算中获取
                "progress_trend": "improving"  # 可以基于历史数据计算
            }

            return learning_progress

        except Exception as e:
            logger.error(f"获取学习进度失败: {e}")
            return {
                "total_learning_sessions": 0,
                "today_learning_sessions": 0,
                "error": str(e)
            }

    async def _get_performance_metrics(self) -> Dict[str, Any]:
        """获取性能监控指标"""
        try:
            # 从核心系统集成获取绩效报告
            from .core.core_systems_integration import yaoguang_core_systems

            performance_report = await yaoguang_core_systems.get_performance_report(period_days=7)

            if performance_report:
                return {
                    "weekly_performance": performance_report,
                    "system_health": yaoguang_core_systems.is_healthy(),
                    "integration_score": yaoguang_core_systems.get_integration_status().get("integration_score", 0),
                    "last_performance_update": datetime.now().isoformat()
                }
            else:
                return {
                    "system_health": yaoguang_core_systems.is_healthy(),
                    "integration_score": yaoguang_core_systems.get_integration_status().get("integration_score", 0),
                    "performance_status": "no_data",
                    "last_performance_update": datetime.now().isoformat()
                }

        except Exception as e:
            logger.error(f"获取性能指标失败: {e}")
            return {
                "system_health": False,
                "error": str(e),
                "last_performance_update": datetime.now().isoformat()
            }

    async def update_learning_status(self, learning_focus: str, research_active: bool = True) -> Dict[str, Any]:
        """更新学习状态"""
        try:
            self.current_learning_focus = learning_focus
            self.active_research = research_active

            # 记录状态变更到持久化系统
            from .core.data_persistence import yaoguang_persistence

            await yaoguang_persistence.save_task_result({
                "task_type": "status_update",
                "task_id": f"status_{datetime.now().strftime('%Y%m%d_%H%M%S')}",
                "status": "completed",
                "result": {
                    "learning_focus": learning_focus,
                    "research_active": research_active,
                    "update_time": datetime.now().isoformat()
                }
            })

            # 记录到核心系统
            from .core.core_systems_integration import yaoguang_core_systems
            await yaoguang_core_systems.add_memory(
                f"学习状态更新: 焦点={learning_focus}, 研究活跃={research_active}",
                "system_notification"
            )

            logger.info(f"瑶光星学习状态更新: {learning_focus}")

            return {
                "success": True,
                "learning_focus": learning_focus,
                "research_active": research_active,
                "update_time": datetime.now().isoformat()
            }

        except Exception as e:
            logger.error(f"更新学习状态失败: {e}")
            return {
                "success": False,
                "error": str(e)
            }

    async def record_learning_session(self, session_data: Dict[str, Any]) -> Dict[str, Any]:
        """记录学习会话"""
        try:
            # 保存学习会话数据
            from .core.data_persistence import yaoguang_persistence

            session_id = f"learning_{datetime.now().strftime('%Y%m%d_%H%M%S')}"

            await yaoguang_persistence.save_task_result({
                "task_type": "learning_summary",
                "task_id": session_id,
                "status": "completed",
                "result": session_data
            })

            # 记录绩效
            from .core.core_systems_integration import yaoguang_core_systems

            learning_efficiency = session_data.get("efficiency", 0.85)
            await yaoguang_core_systems.record_learning_performance(
                "learning_efficiency",
                learning_efficiency,
                session_data
            )

            # 添加记忆
            await yaoguang_core_systems.add_memory(
                f"学习会话完成: {session_data.get('summary', '无摘要')}",
                "learning_record"
            )

            logger.info(f"学习会话记录完成: {session_id}")

            return {
                "success": True,
                "session_id": session_id,
                "efficiency": learning_efficiency,
                "timestamp": datetime.now().isoformat()
            }

        except Exception as e:
            logger.error(f"记录学习会话失败: {e}")
            return {
                "success": False,
                "error": str(e)
            }



# 全局实例
yaoguang_star_service = YaoguangStarService()
