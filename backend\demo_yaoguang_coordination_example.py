#!/usr/bin/env python3
"""
瑶光星协调机制实际演示
展示瑶光星如何具体协调6星进行学习和回测
"""

import sys
import asyncio
import json
from datetime import datetime
sys.path.append('.')

async def demo_coordination_mechanism():
    """演示瑶光星协调机制"""
    try:
        from roles.yaoguang_star.yaoguang_coordination_service import yaoguang_coordination_service
        
        print("🌟" + "="*80)
        print("🌟 瑶光星智能体协调机制实际演示")
        print("🌟" + "="*80)
        
        print(f"\n📋 瑶光星智能体信息:")
        print(f"   服务名称: {yaoguang_coordination_service.service_name}")
        print(f"   版本: {yaoguang_coordination_service.version}")
        print(f"   服务类型: {yaoguang_coordination_service.service_type}")
        print(f"   智能体角色: 七星系统总指挥官和协调中心")
        
        # 演示1：学习协调机制
        print("\n" + "="*60)
        print("📚 演示1：瑶光星学习协调机制")
        print("="*60)
        
        print("\n🎯 瑶光星智能体分析学习需求...")
        learning_config = {
            "learning_focus": ["pattern_recognition", "risk_assessment", "strategy_optimization"],
            "learning_mode": "comprehensive",
            "use_detailed_flow": True,
            "priority": "high",
            "requester": "demo_system",
            "objectives": {
                "accuracy_target": 0.85,
                "risk_control": 0.10,
                "return_target": 0.15
            },
            "data_range": {
                "start_date": "2024-01-01",
                "end_date": "2024-12-31"
            }
        }
        
        print(f"   学习重点: {learning_config['learning_focus']}")
        print(f"   学习模式: {learning_config['learning_mode']}")
        print(f"   准确度目标: {learning_config['objectives']['accuracy_target']:.1%}")
        print(f"   风险控制: {learning_config['objectives']['risk_control']:.1%}")
        
        print("\n🚀 瑶光星启动学习协调流程...")
        learning_result = await yaoguang_coordination_service.coordinate_learning_session(learning_config)
        
        if learning_result.get('success'):
            print("✅ 学习协调成功完成！")
            print(f"   协调请求ID: {learning_result['request_id']}")
            print(f"   执行时间: {learning_result['execution_time']:.2f}秒")
            print(f"   使用协调器: {learning_result['coordinator_used']}")
            
            coord_result = learning_result['coordination_result']
            if coord_result.get('success'):
                learning_res = coord_result.get('learning_result', {})
                if learning_res.get('success'):
                    print(f"\n📊 学习协调详细结果:")
                    print(f"   学习目标数: {coord_result.get('objectives_count', 0)}")
                    print(f"   目标达成数: {learning_res.get('objectives_achieved', 0)}")
                    print(f"   知识获得量: {learning_res.get('knowledge_gained', 0)}")
                    print(f"   总耗时: {learning_res.get('total_duration', '未知')}")
                    
                    # 显示各阶段执行情况
                    learning_results = learning_res.get('learning_results', {})
                    print(f"\n🔍 各学习阶段执行情况:")
                    for stage, stage_result in learning_results.items():
                        status = '✅' if stage_result.get('success') else '❌'
                        print(f"   {status} {stage}: {stage_result.get('success', False)}")
                        
                        if stage == 'analysis' and stage_result.get('success'):
                            print(f"      - 完成星数: {stage_result.get('stars_completed', 0)}")
                            print(f"      - 学习模式数: {stage_result.get('total_patterns_learned', 0)}")
                        elif stage == 'strategy_learning' and stage_result.get('success'):
                            print(f"      - 策略优化: {stage_result.get('strategies_optimized', 0)}")
                            print(f"      - 执行算法: {stage_result.get('execution_algorithms_learned', 0)}")
        
        # 演示2：回测协调机制
        print("\n" + "="*60)
        print("🔄 演示2：瑶光星回测协调机制")
        print("="*60)
        
        print("\n🎯 瑶光星智能体制定回测计划...")
        backtest_config = {
            "strategy_name": "AI驱动多因子策略",
            "start_date": "2024-01-01",
            "end_date": "2024-12-31",
            "initial_capital": 1000000.0,
            "benchmark": "000300.XSHG",
            "universe_size": 100,
            "rebalance_frequency": "monthly",
            "commission_rate": 0.0003,
            "slippage_rate": 0.001,
            "max_position_size": 0.1,
            "priority": "high",
            "requester": "demo_system"
        }
        
        print(f"   策略名称: {backtest_config['strategy_name']}")
        print(f"   回测期间: {backtest_config['start_date']} 至 {backtest_config['end_date']}")
        print(f"   初始资金: {backtest_config['initial_capital']:,.0f} 元")
        print(f"   基准指数: {backtest_config['benchmark']}")
        print(f"   股票池大小: {backtest_config['universe_size']}")
        
        print("\n🚀 瑶光星启动回测协调流程...")
        backtest_result = await yaoguang_coordination_service.coordinate_backtest_session(backtest_config)
        
        if backtest_result.get('success'):
            print("✅ 回测协调成功完成！")
            print(f"   协调请求ID: {backtest_result['request_id']}")
            print(f"   执行时间: {backtest_result['execution_time']:.2f}秒")
            print(f"   使用协调器: {backtest_result['coordinator_used']}")
            
            coord_result = backtest_result['coordination_result']
            if coord_result.get('success'):
                backtest_res = coord_result.get('backtest_result', {})
                if backtest_res.get('success'):
                    print(f"\n📊 回测协调详细结果:")
                    print(f"   回测期间: {coord_result.get('backtest_period', '未知')}")
                    print(f"   时间线点数: {backtest_res.get('timeline_points', 0)}")
                    print(f"   总耗时: {backtest_res.get('total_duration', '未知')}")
                    
                    # 显示各阶段执行情况
                    backtest_results = backtest_res.get('backtest_results', {})
                    print(f"\n🔍 各回测阶段执行情况:")
                    for stage, stage_result in backtest_results.items():
                        status = '✅' if stage_result.get('success') else '❌'
                        print(f"   {status} {stage}: {stage_result.get('success', False)}")
                        
                        if stage == 'historical_analysis' and stage_result.get('success'):
                            print(f"      - 完成星数: {stage_result.get('stars_completed', 0)}")
                            print(f"      - 历史决策数: {stage_result.get('total_historical_decisions', 0)}")
                        elif stage == 'strategy_simulation' and stage_result.get('success'):
                            print(f"      - 组合决策: {stage_result.get('portfolio_decisions', 0)}")
                            print(f"      - 执行交易: {stage_result.get('execution_transactions', 0)}")
                    
                    # 显示综合报告
                    comp_report = backtest_res.get('comprehensive_report', {})
                    exec_summary = comp_report.get('executive_summary', {})
                    if exec_summary:
                        key_metrics = exec_summary.get('key_metrics', {})
                        if key_metrics:
                            print(f"\n📈 策略绩效指标:")
                            print(f"   总收益率: {key_metrics.get('total_return', 0):.1%}")
                            print(f"   夏普比率: {key_metrics.get('sharpe_ratio', 0):.2f}")
                            print(f"   最大回撤: {key_metrics.get('max_drawdown', 0):.1%}")
        
        # 演示3：反馈分发机制
        print("\n" + "="*60)
        print("📤 演示3：瑶光星反馈分发机制")
        print("="*60)
        
        if learning_result.get('success'):
            print("\n🎯 瑶光星智能体分析学习结果，准备反馈...")
            feedback_config = {
                "feedback_type": "learning",
                "source_data": {
                    "session_id": learning_result["request_id"],
                    "coordination_result": learning_result["coordination_result"],
                    "session_type": "learning"
                }
            }
            
            print("🚀 瑶光星启动智能反馈分发...")
            feedback_result = await yaoguang_coordination_service.distribute_feedback(feedback_config)
            
            if feedback_result.get('success'):
                print("✅ 反馈分发成功完成！")
                print(f"   反馈请求ID: {feedback_result['request_id']}")
                print(f"   反馈类型: {feedback_result['feedback_type']}")
                
                feedback_res = feedback_result.get('feedback_result', {})
                if feedback_res.get('success'):
                    print(f"\n📊 反馈分发详细结果:")
                    print(f"   反馈项数: {feedback_res.get('feedback_items_generated', 0)}")
                    print(f"   目标星数: {feedback_res.get('stars_targeted', 0)}")
                    
                    distribution_result = feedback_res.get('distribution_result', {})
                    if distribution_result.get('success'):
                        print(f"   成功分发: {distribution_result.get('successful_distributions', 0)}")
                        print(f"   总分发数: {distribution_result.get('total_distributions', 0)}")
                        print(f"   成功率: {distribution_result.get('success_rate', 0):.1%}")
                        
                        print(f"\n🌟 各星反馈分发情况:")
                        dist_results = distribution_result.get('distribution_results', {})
                        for star, result in dist_results.items():
                            status = '✅' if result.get('success') else '❌'
                            method = result.get('delivery_method', 'unknown')
                            items = result.get('items_delivered', result.get('items_scheduled', 0))
                            print(f"   {status} {star}: {method} 方式，{items} 项反馈")
        
        # 演示4：协调状态和性能
        print("\n" + "="*60)
        print("📊 演示4：瑶光星协调状态和性能")
        print("="*60)
        
        print("\n🔍 瑶光星智能体当前状态:")
        status = await yaoguang_coordination_service.get_coordination_status()
        
        service_info = status.get("service_info", {})
        print(f"   服务运行时间: {service_info.get('uptime', '未知')}")
        
        coord_status = status.get("coordination_status", {})
        print(f"   活跃协调: {coord_status.get('active_coordinations', 0)}")
        print(f"   待处理请求: {coord_status.get('pending_requests', 0)}")
        print(f"   已完成协调: {coord_status.get('completed_coordinations', 0)}")
        
        session_stats = status.get("session_statistics", {})
        print(f"\n📈 协调会话统计:")
        print(f"   学习会话: {session_stats.get('learning', 0)}")
        print(f"   回测会话: {session_stats.get('backtest', 0)}")
        print(f"   反馈会话: {session_stats.get('feedback', 0)}")
        
        perf_metrics = status.get("performance_metrics", {})
        print(f"\n⚡ 性能指标:")
        print(f"   总协调数: {perf_metrics.get('total_coordinations', 0)}")
        print(f"   成功协调数: {perf_metrics.get('successful_coordinations', 0)}")
        print(f"   平均协调时间: {perf_metrics.get('average_coordination_time', 0):.2f}秒")
        print(f"   星际协作得分: {perf_metrics.get('star_collaboration_score', 0):.2f}")
        
        # 演示5：协调历史
        print("\n🕒 最近协调历史:")
        history = await yaoguang_coordination_service.get_coordination_history(3)
        
        for i, record in enumerate(history, 1):
            print(f"   {i}. {record['request_type']} - {record['status']} ({record['execution_time']:.2f}s)")
            print(f"      请求者: {record['requester']}, 优先级: {record['priority']}")
        
        print("\n" + "="*80)
        print("🎉 瑶光星智能体协调机制演示完成！")
        print("🎉 瑶光星成功展示了作为七星系统总指挥官的强大协调能力！")
        print("="*80)
        
    except Exception as e:
        print(f"❌ 演示执行失败: {e}")
        import traceback
        traceback.print_exc()

if __name__ == "__main__":
    asyncio.run(demo_coordination_mechanism())
