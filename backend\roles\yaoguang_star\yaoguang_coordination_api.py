"""
瑶光星协调API
为瑶光星协调服务提供RESTful API接口
"""

from fastapi import APIRouter, HTTPException, BackgroundTasks
from pydantic import BaseModel
from typing import Dict, List, Any, Optional
from datetime import datetime

from .yaoguang_coordination_service import yaoguang_coordination_service

# 创建路由器
router = APIRouter(prefix="/yaoguang", tags=["瑶光星协调"])

# 请求模型
class LearningCoordinationRequest(BaseModel):
    """学习协调请求"""
    learning_focus: List[str] = ["pattern_recognition", "risk_assessment", "strategy_optimization"]
    learning_mode: str = "comprehensive"
    use_detailed_flow: bool = False
    priority: str = "medium"
    requester: str = "api"
    objectives: Optional[Dict[str, Any]] = None
    data_range: Optional[Dict[str, str]] = None

class BacktestCoordinationRequest(BaseModel):
    """回测协调请求"""
    strategy_name: str
    start_date: str
    end_date: str
    initial_capital: float = 1000000.0
    benchmark: str = "000300.XSHG"
    universe_size: int = 100
    rebalance_frequency: str = "monthly"
    commission_rate: float = 0.0003
    slippage_rate: float = 0.001
    max_position_size: float = 0.1
    priority: str = "medium"
    requester: str = "api"

class FeedbackDistributionRequest(BaseModel):
    """反馈分发请求"""
    feedback_type: str  # 'learning' or 'backtest'
    source_session_id: str
    source_data: Dict[str, Any]
    target_stars: Optional[List[str]] = None

# API端点
@router.get("/status")
async def get_coordination_status():
    """获取协调服务状态"""
    try:
        status = await yaoguang_coordination_service.get_coordination_status()
        return {
            "success": True,
            "data": status,
            "timestamp": datetime.now().isoformat()
        }
    except Exception as e:
        raise HTTPException(status_code=500, detail=str(e))

@router.post("/coordinate/learning")
async def coordinate_learning(request: LearningCoordinationRequest, background_tasks: BackgroundTasks):
    """协调学习会话"""
    try:
        # 转换请求为配置字典
        learning_config = {
            "learning_focus": request.learning_focus,
            "learning_mode": request.learning_mode,
            "use_detailed_flow": request.use_detailed_flow,
            "priority": request.priority,
            "requester": request.requester,
            "objectives": request.objectives or {},
            "data_range": request.data_range or {}
        }
        
        # 启动学习协调
        result = await yaoguang_coordination_service.coordinate_learning_session(learning_config)
        
        # 如果有待处理请求，在后台处理
        if yaoguang_coordination_service.pending_requests:
            background_tasks.add_task(yaoguang_coordination_service.process_pending_requests)
        
        return {
            "success": result.get("success", False),
            "data": result,
            "timestamp": datetime.now().isoformat()
        }
        
    except Exception as e:
        raise HTTPException(status_code=500, detail=str(e))

@router.post("/coordinate/backtest")
async def coordinate_backtest(request: BacktestCoordinationRequest, background_tasks: BackgroundTasks):
    """协调回测会话"""
    try:
        # 转换请求为配置字典
        backtest_config = {
            "strategy_name": request.strategy_name,
            "start_date": request.start_date,
            "end_date": request.end_date,
            "initial_capital": request.initial_capital,
            "benchmark": request.benchmark,
            "universe_size": request.universe_size,
            "rebalance_frequency": request.rebalance_frequency,
            "commission_rate": request.commission_rate,
            "slippage_rate": request.slippage_rate,
            "max_position_size": request.max_position_size,
            "priority": request.priority,
            "requester": request.requester
        }
        
        # 启动回测协调
        result = await yaoguang_coordination_service.coordinate_backtest_session(backtest_config)
        
        # 如果有待处理请求，在后台处理
        if yaoguang_coordination_service.pending_requests:
            background_tasks.add_task(yaoguang_coordination_service.process_pending_requests)
        
        return {
            "success": result.get("success", False),
            "data": result,
            "timestamp": datetime.now().isoformat()
        }
        
    except Exception as e:
        raise HTTPException(status_code=500, detail=str(e))

@router.post("/distribute/feedback")
async def distribute_feedback(request: FeedbackDistributionRequest):
    """分发反馈"""
    try:
        # 转换请求为配置字典
        feedback_config = {
            "feedback_type": request.feedback_type,
            "source_data": {
                "session_id": request.source_session_id,
                **request.source_data
            }
        }
        
        # 分发反馈
        result = await yaoguang_coordination_service.distribute_feedback(feedback_config)
        
        return {
            "success": result.get("success", False),
            "data": result,
            "timestamp": datetime.now().isoformat()
        }
        
    except Exception as e:
        raise HTTPException(status_code=500, detail=str(e))

@router.get("/history")
async def get_coordination_history(limit: int = 10):
    """获取协调历史"""
    try:
        history = await yaoguang_coordination_service.get_coordination_history(limit)
        return {
            "success": True,
            "data": {
                "history": history,
                "total_records": len(history)
            },
            "timestamp": datetime.now().isoformat()
        }
    except Exception as e:
        raise HTTPException(status_code=500, detail=str(e))

@router.get("/engines/status")
async def get_engines_status():
    """获取各协调引擎状态"""
    try:
        status = {
            "seven_star_coordinator": {
                "name": yaoguang_coordination_service.seven_star_coordinator.coordinator_name,
                "version": yaoguang_coordination_service.seven_star_coordinator.version,
                "active_sessions": len(yaoguang_coordination_service.seven_star_coordinator.active_sessions),
                "completed_sessions": len(yaoguang_coordination_service.seven_star_coordinator.completed_sessions)
            },
            "learning_coordinator": {
                "name": yaoguang_coordination_service.learning_coordinator.coordinator_name,
                "version": yaoguang_coordination_service.learning_coordinator.version,
                "active_sessions": len(yaoguang_coordination_service.learning_coordinator.active_learning_sessions),
                "completed_sessions": len(yaoguang_coordination_service.learning_coordinator.completed_learning_sessions)
            },
            "backtest_coordinator": {
                "name": yaoguang_coordination_service.backtest_coordinator.coordinator_name,
                "version": yaoguang_coordination_service.backtest_coordinator.version,
                "active_sessions": len(yaoguang_coordination_service.backtest_coordinator.active_backtest_sessions),
                "completed_sessions": len(yaoguang_coordination_service.backtest_coordinator.completed_backtest_sessions)
            },
            "feedback_engine": {
                "name": yaoguang_coordination_service.feedback_engine.engine_name,
                "version": yaoguang_coordination_service.feedback_engine.version,
                "active_sessions": len(yaoguang_coordination_service.feedback_engine.active_feedback_sessions),
                "completed_sessions": len(yaoguang_coordination_service.feedback_engine.completed_feedback_sessions)
            }
        }
        
        return {
            "success": True,
            "data": status,
            "timestamp": datetime.now().isoformat()
        }
    except Exception as e:
        raise HTTPException(status_code=500, detail=str(e))

@router.get("/capabilities")
async def get_coordination_capabilities():
    """获取协调能力"""
    try:
        capabilities = {
            "coordination_types": ["learning", "backtest", "feedback"],
            "learning_capabilities": {
                "learning_modes": ["comprehensive", "focused", "adaptive"],
                "learning_focus_areas": ["pattern_recognition", "risk_assessment", "strategy_optimization"],
                "supported_stars": ["kaiyang", "tianshu", "tianji", "tianxuan", "tianquan", "yuheng"]
            },
            "backtest_capabilities": {
                "strategy_types": ["momentum", "mean_reversion", "multi_factor"],
                "rebalance_frequencies": ["daily", "weekly", "monthly"],
                "supported_benchmarks": ["000300.XSHG", "000905.XSHG", "000852.XSHG"],
                "analysis_features": ["performance_attribution", "risk_analysis", "execution_analysis"]
            },
            "feedback_capabilities": {
                "feedback_types": ["learning_feedback", "backtest_feedback", "performance_feedback"],
                "delivery_methods": ["immediate", "batch", "scheduled"],
                "target_stars": ["kaiyang", "tianshu", "tianji", "tianxuan", "tianquan", "yuheng"]
            },
            "performance_features": {
                "concurrent_coordinations": yaoguang_coordination_service.coordination_config["max_concurrent_coordinations"],
                "auto_feedback": yaoguang_coordination_service.coordination_config["enable_auto_feedback"],
                "cross_session_learning": yaoguang_coordination_service.coordination_config["enable_cross_session_learning"]
            }
        }
        
        return {
            "success": True,
            "data": capabilities,
            "timestamp": datetime.now().isoformat()
        }
    except Exception as e:
        raise HTTPException(status_code=500, detail=str(e))

@router.post("/shutdown")
async def shutdown_coordination_service():
    """关闭协调服务"""
    try:
        result = await yaoguang_coordination_service.shutdown_service()
        return {
            "success": result.get("success", False),
            "data": result,
            "timestamp": datetime.now().isoformat()
        }
    except Exception as e:
        raise HTTPException(status_code=500, detail=str(e))
