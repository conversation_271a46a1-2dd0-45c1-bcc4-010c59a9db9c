#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
瑶光星统一服务单元测试
"""

import unittest
import asyncio
import pandas as pd
from datetime import datetime, timedelta
from unittest.mock import Mock, patch, AsyncMock
import sys
import os

# 添加项目路径
sys.path.append(os.path.dirname(os.path.dirname(os.path.dirname(os.path.dirname(__file__)))))

from roles.yaoguang_star.services.unified_learning_service import UnifiedLearningService
from roles.yaoguang_star.services.unified_parameter_optimization_service import UnifiedParameterOptimizationService
from roles.yaoguang_star.services.unified_time_control_service import UnifiedTimeControlService, TimeMode

class TestUnifiedLearningService(unittest.TestCase):
    """统一学习服务测试"""
    
    def setUp(self):
        """测试前准备"""
        self.learning_service = UnifiedLearningService()
    
    def test_init(self):
        """测试初始化"""
        self.assertEqual(self.learning_service.service_name, "瑶光星统一学习服务")
        self.assertEqual(self.learning_service.version, "1.0.0")
        self.assertIsInstance(self.learning_service.learning_records, dict)
        self.assertIsInstance(self.learning_service.learning_sessions, dict)
        self.assertIsInstance(self.learning_service.learning_config, dict)
    
    async def test_create_learning_session_success(self):
        """测试成功创建学习会话"""
        session_type = "test_learning"
        config = {"test_mode": True}
        
        result = await self.learning_service.create_learning_session(session_type, config)
        
        self.assertTrue(result["success"])
        self.assertIn("session_id", result)
        self.assertEqual(result["session_type"], session_type)
        self.assertIn("start_time", result)
        
        # 验证会话已创建
        session_id = result["session_id"]
        self.assertIn(session_id, self.learning_service.learning_sessions)
    
    async def test_create_learning_session_max_limit(self):
        """测试达到最大会话限制"""
        # 创建最大数量的会话
        max_sessions = self.learning_service.learning_config["max_concurrent_sessions"]
        for i in range(max_sessions):
            await self.learning_service.create_learning_session(f"test_{i}")
        
        # 尝试创建超出限制的会话
        result = await self.learning_service.create_learning_session("overflow")
        
        self.assertFalse(result["success"])
        self.assertIn("已达到最大并发学习会话数量", result["error"])
    
    async def test_execute_stock_learning_success(self):
        """测试成功执行个股学习"""
        stock_code = "000001"
        
        # 模拟数据管理器
        mock_data_manager = Mock()
        mock_data_manager.get_real_stock_data = AsyncMock(return_value=pd.DataFrame({
            'close_price': [10.0, 10.1, 10.2, 9.9, 10.3],
            'volume': [1000000, 1100000, 1200000, 900000, 1300000]
        }))
        mock_data_manager.get_real_technical_indicators = AsyncMock(return_value=pd.DataFrame({
            'ta_rsi': [50, 55, 60, 45, 65],
            'ta_macd_line': [0.1, 0.2, 0.15, -0.1, 0.3]
        }))
        
        self.learning_service.data_manager = mock_data_manager
        
        result = await self.learning_service.execute_stock_learning(stock_code)
        
        self.assertTrue(result["success"])
        self.assertIn("learning_results", result)
        self.assertEqual(result["learning_results"]["stock_code"], stock_code)
        self.assertIn("learning_insights", result["learning_results"])
    
    async def test_execute_stock_learning_no_data(self):
        """测试无数据时的个股学习"""
        stock_code = "999999"
        
        # 模拟无数据的数据管理器
        mock_data_manager = Mock()
        mock_data_manager.get_real_stock_data = AsyncMock(return_value=pd.DataFrame())
        
        self.learning_service.data_manager = mock_data_manager
        
        result = await self.learning_service.execute_stock_learning(stock_code)
        
        self.assertFalse(result["success"])
        self.assertIn("无法获取股票", result["error"])
    
    async def test_get_learning_status(self):
        """测试获取学习状态"""
        # 创建一个学习会话
        await self.learning_service.create_learning_session("test_status")
        
        status = await self.learning_service.get_learning_status()
        
        self.assertEqual(status["service_name"], "瑶光星统一学习服务")
        self.assertEqual(status["version"], "1.0.0")
        self.assertGreaterEqual(status["active_sessions"], 1)
        self.assertIn("learning_config", status)
        self.assertIn("last_update", status)
    
    async def test_optimize_learning_parameters(self):
        """测试学习参数优化"""
        # 添加一些学习记录
        for i in range(5):
            await self.learning_service._record_learning_result(
                f"00000{i}", {"test": f"result_{i}"}
            )
        
        result = await self.learning_service.optimize_learning_parameters()
        
        self.assertTrue(result["success"])
        self.assertIn("avg_performance", result)
        self.assertIn("optimized_config", result)

class TestUnifiedParameterOptimizationService(unittest.TestCase):
    """统一参数优化服务测试"""
    
    def setUp(self):
        """测试前准备"""
        self.optimization_service = UnifiedParameterOptimizationService()
    
    def test_init(self):
        """测试初始化"""
        self.assertEqual(self.optimization_service.service_name, "瑶光星统一参数优化服务")
        self.assertEqual(self.optimization_service.version, "1.0.0")
        self.assertIn("learning_parameters", self.optimization_service.current_parameters)
        self.assertIn("backtest_parameters", self.optimization_service.current_parameters)
    
    async def test_optimize_learning_parameters(self):
        """测试学习参数优化"""
        test_data = pd.DataFrame({
            'feature1': [1, 2, 3, 4, 5],
            'feature2': [2, 4, 6, 8, 10],
            'target': [0, 1, 0, 1, 0]
        })
        
        result = await self.optimization_service.optimize_learning_parameters(test_data)
        
        self.assertTrue(result["success"])
        self.assertIn("task_id", result)
        self.assertIn("optimized_parameters", result)
        self.assertIn("improvement", result)
        self.assertIn("optimization_time", result)
    
    async def test_optimize_backtest_parameters(self):
        """测试回测参数优化"""
        test_data = pd.DataFrame({
            'date': pd.date_range('2023-01-01', periods=100),
            'price': [100 + i * 0.1 for i in range(100)],
            'volume': [1000000 + i * 1000 for i in range(100)]
        })
        
        result = await self.optimization_service.optimize_backtest_parameters(test_data)
        
        self.assertTrue(result["success"])
        self.assertIn("task_id", result)
        self.assertIn("optimized_parameters", result)
        self.assertIn("improvement", result)
    
    async def test_get_optimization_status(self):
        """测试获取优化状态"""
        status = await self.optimization_service.get_optimization_status()
        
        self.assertEqual(status["service_name"], "瑶光星统一参数优化服务")
        self.assertEqual(status["version"], "1.0.0")
        self.assertIn("active_tasks", status)
        self.assertIn("completed_tasks", status)
        self.assertIn("current_parameters", status)
    
    async def test_get_optimization_history(self):
        """测试获取优化历史"""
        # 先执行一次优化以创建历史记录
        test_data = pd.DataFrame({'test': [1, 2, 3]})
        await self.optimization_service.optimize_learning_parameters(test_data)
        
        history = await self.optimization_service.get_optimization_history(limit=5)
        
        self.assertIsInstance(history, list)
        if history:
            self.assertIn("parameter_name", history[0])
            self.assertIn("improvement", history[0])
            self.assertIn("timestamp", history[0])

class TestUnifiedTimeControlService(unittest.TestCase):
    """统一时间控制服务测试"""
    
    def setUp(self):
        """测试前准备"""
        self.time_service = UnifiedTimeControlService()
    
    def test_init(self):
        """测试初始化"""
        self.assertEqual(self.time_service.service_name, "瑶光星统一时间控制服务")
        self.assertEqual(self.time_service.version, "1.0.0")
        self.assertIsInstance(self.time_service.active_sessions, dict)
        self.assertIn("news_delay", self.time_service.default_time_delays)
    
    async def test_create_time_session_success(self):
        """测试成功创建时间会话"""
        start_time = datetime(2023, 1, 1, 9, 0, 0)
        end_time = datetime(2023, 1, 1, 15, 0, 0)
        
        result = await self.time_service.create_time_session(
            "test_session", start_time, end_time, TimeMode.BACKTEST
        )
        
        self.assertTrue(result["success"])
        self.assertIn("session_id", result)
        self.assertEqual(result["mode"], "backtest")
        self.assertIn("time_delays", result)
        
        # 验证会话已创建
        session_id = result["session_id"]
        self.assertIn(session_id, self.time_service.active_sessions)
    
    async def test_create_time_session_max_limit(self):
        """测试达到最大会话限制"""
        start_time = datetime(2023, 1, 1, 9, 0, 0)
        
        # 创建最大数量的会话
        max_sessions = self.time_service.time_control_config["max_concurrent_sessions"]
        for i in range(max_sessions):
            await self.time_service.create_time_session(f"test_{i}", start_time)
        
        # 尝试创建超出限制的会话
        result = await self.time_service.create_time_session("overflow", start_time)
        
        self.assertFalse(result["success"])
        self.assertIn("已达到最大并发时间会话数量", result["error"])
    
    async def test_advance_time_success(self):
        """测试成功推进时间"""
        start_time = datetime(2023, 1, 1, 9, 0, 0)
        
        # 创建时间会话
        create_result = await self.time_service.create_time_session("test", start_time)
        session_id = create_result["session_id"]
        
        # 推进时间
        time_delta = timedelta(hours=1)
        result = await self.time_service.advance_time(session_id, time_delta)
        
        self.assertTrue(result["success"])
        self.assertEqual(result["session_id"], session_id)
        self.assertIn("old_time", result)
        self.assertIn("new_time", result)
        self.assertEqual(result["time_delta"], 3600.0)  # 1小时 = 3600秒
    
    async def test_advance_time_beyond_end(self):
        """测试推进时间超出结束时间"""
        start_time = datetime(2023, 1, 1, 9, 0, 0)
        end_time = datetime(2023, 1, 1, 10, 0, 0)
        
        # 创建有结束时间的会话
        create_result = await self.time_service.create_time_session(
            "test", start_time, end_time
        )
        session_id = create_result["session_id"]
        
        # 尝试推进超出结束时间
        time_delta = timedelta(hours=2)
        result = await self.time_service.advance_time(session_id, time_delta)
        
        self.assertFalse(result["success"])
        self.assertIn("时间推进超出会话结束时间", result["error"])
    
    async def test_validate_time_access_success(self):
        """测试成功的时间访问验证"""
        start_time = datetime(2023, 1, 1, 9, 0, 0)
        
        # 创建时间会话
        create_result = await self.time_service.create_time_session("test", start_time)
        session_id = create_result["session_id"]
        
        # 验证访问当前时间之前的数据
        requested_time = datetime(2023, 1, 1, 8, 30, 0)
        result = await self.time_service.validate_time_access(session_id, requested_time)
        
        self.assertTrue(result["success"])
        self.assertTrue(result["access_allowed"])
    
    async def test_validate_time_access_future_function(self):
        """测试未来函数检测"""
        start_time = datetime(2023, 1, 1, 9, 0, 0)
        
        # 创建严格模式的时间会话
        create_result = await self.time_service.create_time_session("test", start_time)
        session_id = create_result["session_id"]
        
        # 尝试访问未来数据
        requested_time = datetime(2023, 1, 1, 10, 0, 0)
        result = await self.time_service.validate_time_access(session_id, requested_time)
        
        self.assertFalse(result["success"])
        self.assertEqual(result["violation_type"], "future_function")
        self.assertIn("未来函数检测", result["error"])
    
    async def test_apply_time_delay(self):
        """测试应用时间延迟"""
        start_time = datetime(2023, 1, 1, 9, 0, 0)
        
        # 创建时间会话
        create_result = await self.time_service.create_time_session("test", start_time)
        session_id = create_result["session_id"]
        
        # 应用新闻延迟
        result = await self.time_service.apply_time_delay(session_id, "news_delay")
        
        self.assertTrue(result["success"])
        self.assertEqual(result["delay_type"], "news_delay")
        self.assertIn("delay_seconds", result)
    
    async def test_close_time_session(self):
        """测试关闭时间会话"""
        start_time = datetime(2023, 1, 1, 9, 0, 0)
        
        # 创建时间会话
        create_result = await self.time_service.create_time_session("test", start_time)
        session_id = create_result["session_id"]
        
        # 关闭会话
        result = await self.time_service.close_time_session(session_id)
        
        self.assertTrue(result["success"])
        self.assertEqual(result["session_id"], session_id)
        self.assertIn("session_duration", result)
        
        # 验证会话已移除
        self.assertNotIn(session_id, self.time_service.active_sessions)

def run_async_test(coro):
    """运行异步测试的辅助函数"""
    loop = asyncio.new_event_loop()
    asyncio.set_event_loop(loop)
    try:
        return loop.run_until_complete(coro)
    finally:
        loop.close()

# 为异步测试方法添加装饰器
test_classes = [
    TestUnifiedLearningService,
    TestUnifiedParameterOptimizationService,
    TestUnifiedTimeControlService
]

for cls in test_classes:
    for name, method in cls.__dict__.items():
        if name.startswith('test_') and asyncio.iscoroutinefunction(method):
            setattr(cls, name, lambda self, m=method: run_async_test(m(self)))

if __name__ == '__main__':
    unittest.main()
