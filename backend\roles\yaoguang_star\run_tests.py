#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
瑶光星测试运行器
运行所有单元测试和集成测试
"""

import unittest
import sys
import os
from pathlib import Path

# 添加项目路径
project_root = Path(__file__).parent.parent.parent
sys.path.append(str(project_root))

def run_all_tests():
    """运行所有测试"""
    print("🧪 瑶光星测试套件启动")
    print("=" * 60)
    
    # 测试发现
    test_dir = Path(__file__).parent / "tests"
    loader = unittest.TestLoader()
    
    # 发现所有测试
    suite = loader.discover(str(test_dir), pattern="test_*.py")
    
    # 运行测试
    runner = unittest.TextTestRunner(
        verbosity=2,
        stream=sys.stdout,
        descriptions=True,
        failfast=False
    )
    
    print(f"📁 测试目录: {test_dir}")
    print(f"🔍 发现测试模块...")
    
    result = runner.run(suite)
    
    # 输出测试结果摘要
    print("\n" + "=" * 60)
    print("🎯 瑶光星测试结果摘要")
    print("=" * 60)
    
    print(f"✅ 运行测试总数: {result.testsRun}")
    print(f"❌ 失败测试数量: {len(result.failures)}")
    print(f"💥 错误测试数量: {len(result.errors)}")
    
    if hasattr(result, 'skipped'):
        print(f"⏭️  跳过测试数量: {len(result.skipped)}")
    
    # 详细失败信息
    if result.failures:
        print("\n❌ 失败的测试:")
        for test, traceback in result.failures:
            print(f"  - {test}: {traceback.split('AssertionError:')[-1].strip()}")
    
    # 详细错误信息
    if result.errors:
        print("\n💥 错误的测试:")
        for test, traceback in result.errors:
            print(f"  - {test}: {traceback.split('Exception:')[-1].strip()}")
    
    # 计算成功率
    if result.testsRun > 0:
        success_rate = (result.testsRun - len(result.failures) - len(result.errors)) / result.testsRun * 100
        print(f"\n📊 测试成功率: {success_rate:.1f}%")
        
        if success_rate >= 90:
            print("🎉 测试成功率优秀！")
        elif success_rate >= 70:
            print("👍 测试成功率良好")
        else:
            print("⚠️  测试成功率需要改进")
    
    print("=" * 60)
    
    # 返回测试是否全部通过
    return len(result.failures) == 0 and len(result.errors) == 0

def run_specific_test(test_module):
    """运行特定测试模块"""
    print(f"🧪 运行特定测试模块: {test_module}")
    print("=" * 60)
    
    try:
        # 导入测试模块
        module = __import__(f"tests.{test_module}", fromlist=[test_module])
        
        # 创建测试套件
        loader = unittest.TestLoader()
        suite = loader.loadTestsFromModule(module)
        
        # 运行测试
        runner = unittest.TextTestRunner(verbosity=2)
        result = runner.run(suite)
        
        print(f"\n📊 {test_module} 测试结果:")
        print(f"  运行: {result.testsRun}")
        print(f"  失败: {len(result.failures)}")
        print(f"  错误: {len(result.errors)}")
        
        return len(result.failures) == 0 and len(result.errors) == 0
        
    except ImportError as e:
        print(f"❌ 无法导入测试模块 {test_module}: {e}")
        return False

def main():
    """主函数"""
    if len(sys.argv) > 1:
        # 运行特定测试
        test_module = sys.argv[1]
        success = run_specific_test(test_module)
    else:
        # 运行所有测试
        success = run_all_tests()
    
    # 退出码
    sys.exit(0 if success else 1)

if __name__ == "__main__":
    main()
