#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
预测分析引擎
实现趋势预测、市场预警、智能预测功能
"""

import asyncio
import logging
import json
import numpy as np
from datetime import datetime, timedelta
from typing import Dict, List, Any, Optional, Tuple
from dataclasses import dataclass, asdict
import statistics
from sklearn.linear_model import LinearRegression
from sklearn.ensemble import RandomForestRegressor
import warnings
warnings.filterwarnings('ignore')

logger = logging.getLogger(__name__)

@dataclass
class PredictionResult:
    """预测结果数据结构"""
    prediction_id: str
    prediction_type: str  # 'price_trend', 'volume_forecast', 'volatility_prediction', 'event_probability'
    target: str          # 股票代码或市场指标
    prediction_value: Any
    confidence: float
    prediction_horizon: int  # 预测时间范围（小时）
    input_features: Dict[str, Any]
    model_used: str
    timestamp: datetime
    
    def to_dict(self) -> Dict[str, Any]:
        result = asdict(self)
        result['timestamp'] = self.timestamp.isoformat()
        return result

@dataclass
class TrendAnalysis:
    """趋势分析数据结构"""
    trend_id: str
    target: str
    trend_direction: str  # 'upward', 'downward', 'sideways'
    trend_strength: float  # 0-1
    trend_duration: int   # 预期持续时间（小时）
    key_factors: List[str]
    risk_level: str      # 'low', 'medium', 'high'
    confidence: float
    timestamp: datetime

class PredictiveAnalysisEngine:
    """预测分析引擎 - 天枢星的预测大脑"""
    
    def __init__(self):
        self.engine_name = "预测分析引擎"
        self.version = "1.0.0"
        
        # 预测配置
        self.prediction_active = False
        self.prediction_models = {}
        self.prediction_history: List[PredictionResult] = []
        self.trend_analysis_cache: Dict[str, TrendAnalysis] = {}
        
        # 预测参数
        self.prediction_horizons = [1, 4, 24, 72, 168]  # 1小时、4小时、1天、3天、1周
        self.confidence_threshold = 0.6
        self.trend_sensitivity = 0.05  # 5%变化认为是趋势
        
        # 特征工程
        self.feature_extractors = {
            'price_features': self._extract_price_features,
            'volume_features': self._extract_volume_features,
            'technical_features': self._extract_technical_features,
            'sentiment_features': self._extract_sentiment_features,
            'macro_features': self._extract_macro_features
        }
        
        # 预测模型
        self.models = {
            'linear_regression': LinearRegression(),
            'random_forest': RandomForestRegressor(n_estimators=100, random_state=42),
            'ensemble': None  # 集成模型
        }
        
        # 预测统计
        self.prediction_stats = {
            "total_predictions": 0,
            "accurate_predictions": 0,
            "accuracy_rate": 0.0,
            "avg_confidence": 0.0,
            "model_performance": {}
        }
        
        logger.info(f"✅ {self.engine_name} v{self.version} 初始化完成")
    
    async def start_predictive_analysis(self):
        """启动预测分析"""
        if self.prediction_active:
            logger.warning("预测分析已在运行中")
            return
        
        self.prediction_active = True
        logger.info("🔮 启动预测分析引擎")
        
        # 启动预测任务
        tasks = [
            asyncio.create_task(self._continuous_prediction_loop()),
            asyncio.create_task(self._trend_analysis_loop()),
            asyncio.create_task(self._model_training_loop()),
            asyncio.create_task(self._prediction_validation_loop())
        ]
        
        try:
            await asyncio.gather(*tasks)
        except Exception as e:
            logger.error(f"预测分析任务异常: {e}")
            self.prediction_active = False
    
    async def stop_predictive_analysis(self):
        """停止预测分析"""
        self.prediction_active = False
        logger.info("⏹️ 预测分析引擎已停止")
    
    async def predict_price_trend(self, stock_code: str, horizon_hours: int = 24) -> PredictionResult:
        """预测价格趋势"""
        try:
            # 获取历史数据
            historical_data = await self._get_historical_data(stock_code, days=30)
            
            if not historical_data:
                raise ValueError(f"无法获取 {stock_code} 的历史数据")
            
            # 特征提取
            features = await self._extract_all_features(historical_data)
            
            # 选择最佳模型
            best_model = await self._select_best_model('price_prediction', features)
            
            # 执行预测
            prediction_value = await self._execute_prediction(best_model, features, 'price')
            
            # 计算置信度
            confidence = await self._calculate_prediction_confidence(
                best_model, features, historical_data
            )
            
            # 创建预测结果
            prediction = PredictionResult(
                prediction_id=f"price_{stock_code}_{datetime.now().strftime('%Y%m%d_%H%M%S')}",
                prediction_type="price_trend",
                target=stock_code,
                prediction_value={
                    "direction": "upward" if prediction_value > 0 else "downward",
                    "magnitude": abs(prediction_value),
                    "target_price": historical_data[-1]['price'] * (1 + prediction_value)
                },
                confidence=confidence,
                prediction_horizon=horizon_hours,
                input_features=features,
                model_used=best_model,
                timestamp=datetime.now()
            )
            
            # 记录预测
            self.prediction_history.append(prediction)
            self.prediction_stats["total_predictions"] += 1
            
            logger.info(f"🔮 价格趋势预测完成: {stock_code} - {prediction.prediction_value['direction']}")
            
            return prediction
            
        except Exception as e:
            logger.error(f"价格趋势预测失败: {e}")
            return None
    
    async def predict_volume_forecast(self, stock_code: str, horizon_hours: int = 24) -> PredictionResult:
        """预测成交量"""
        try:
            # 获取历史数据
            historical_data = await self._get_historical_data(stock_code, days=30)
            
            if not historical_data:
                raise ValueError(f"无法获取 {stock_code} 的历史数据")
            
            # 提取成交量特征
            volume_features = await self._extract_volume_features(historical_data)
            price_features = await self._extract_price_features(historical_data)
            
            features = {**volume_features, **price_features}
            
            # 预测成交量
            volume_prediction = await self._execute_prediction(
                'random_forest', features, 'volume'
            )
            
            # 计算置信度
            confidence = await self._calculate_prediction_confidence(
                'random_forest', features, historical_data
            )
            
            prediction = PredictionResult(
                prediction_id=f"volume_{stock_code}_{datetime.now().strftime('%Y%m%d_%H%M%S')}",
                prediction_type="volume_forecast",
                target=stock_code,
                prediction_value={
                    "predicted_volume": volume_prediction,
                    "volume_change": (volume_prediction - historical_data[-1]['volume']) / historical_data[-1]['volume'],
                    "volume_level": "high" if volume_prediction > np.mean([d['volume'] for d in historical_data[-7:]]) * 1.5 else "normal"
                },
                confidence=confidence,
                prediction_horizon=horizon_hours,
                input_features=features,
                model_used='random_forest',
                timestamp=datetime.now()
            )
            
            self.prediction_history.append(prediction)
            self.prediction_stats["total_predictions"] += 1
            
            logger.info(f"📊 成交量预测完成: {stock_code}")
            
            return prediction
            
        except Exception as e:
            logger.error(f"成交量预测失败: {e}")
            return None
    
    async def predict_volatility(self, stock_code: str, horizon_hours: int = 24) -> PredictionResult:
        """预测波动率"""
        try:
            # 获取历史数据
            historical_data = await self._get_historical_data(stock_code, days=30)
            
            if not historical_data:
                raise ValueError(f"无法获取 {stock_code} 的历史数据")
            
            # 计算历史波动率
            price_changes = []
            for i in range(1, len(historical_data)):
                change = (historical_data[i]['price'] - historical_data[i-1]['price']) / historical_data[i-1]['price']
                price_changes.append(change)
            
            # 计算滚动波动率
            volatility_window = 7
            volatilities = []
            for i in range(volatility_window, len(price_changes)):
                window_changes = price_changes[i-volatility_window:i]
                volatility = np.std(window_changes)
                volatilities.append(volatility)
            
            if len(volatilities) < 5:
                raise ValueError("历史数据不足以计算波动率")
            
            # 预测未来波动率
            recent_volatility = np.mean(volatilities[-5:])
            trend_factor = 1.0
            
            # 基于最近趋势调整
            if len(volatilities) >= 3:
                if volatilities[-1] > volatilities[-2] > volatilities[-3]:
                    trend_factor = 1.2  # 波动率上升趋势
                elif volatilities[-1] < volatilities[-2] < volatilities[-3]:
                    trend_factor = 0.8  # 波动率下降趋势
            
            predicted_volatility = recent_volatility * trend_factor
            
            # 计算置信度
            volatility_stability = 1 - (np.std(volatilities[-5:]) / np.mean(volatilities[-5:]))
            confidence = max(0.3, min(0.9, volatility_stability))
            
            prediction = PredictionResult(
                prediction_id=f"volatility_{stock_code}_{datetime.now().strftime('%Y%m%d_%H%M%S')}",
                prediction_type="volatility_prediction",
                target=stock_code,
                prediction_value={
                    "predicted_volatility": predicted_volatility,
                    "volatility_level": "high" if predicted_volatility > np.mean(volatilities) * 1.5 else "normal",
                    "risk_assessment": "elevated" if predicted_volatility > recent_volatility * 1.3 else "stable"
                },
                confidence=confidence,
                prediction_horizon=horizon_hours,
                input_features={"recent_volatilities": volatilities[-5:]},
                model_used='statistical_model',
                timestamp=datetime.now()
            )
            
            self.prediction_history.append(prediction)
            self.prediction_stats["total_predictions"] += 1
            
            logger.info(f"📈 波动率预测完成: {stock_code}")
            
            return prediction
            
        except Exception as e:
            logger.error(f"波动率预测失败: {e}")
            return None
    
    async def analyze_market_trend(self, targets: List[str] = None) -> Dict[str, TrendAnalysis]:
        """分析市场趋势"""
        try:
            if not targets:
                targets = ["000001", "000002", "000858"]  # 默认分析股票
            
            trend_analyses = {}
            
            for target in targets:
                try:
                    # 获取历史数据
                    historical_data = await self._get_historical_data(target, days=30)
                    
                    if not historical_data or len(historical_data) < 10:
                        continue
                    
                    # 趋势分析
                    trend_analysis = await self._perform_trend_analysis(target, historical_data)
                    
                    if trend_analysis:
                        trend_analyses[target] = trend_analysis
                        self.trend_analysis_cache[target] = trend_analysis
                
                except Exception as e:
                    logger.error(f"分析 {target} 趋势失败: {e}")
                    continue
            
            logger.info(f"📊 市场趋势分析完成: {len(trend_analyses)} 个目标")
            
            return trend_analyses
            
        except Exception as e:
            logger.error(f"市场趋势分析失败: {e}")
            return {}
    
    async def _perform_trend_analysis(self, target: str, historical_data: List[Dict]) -> TrendAnalysis:
        """执行趋势分析"""
        try:
            prices = [d['price'] for d in historical_data]
            volumes = [d['volume'] for d in historical_data]
            
            # 计算移动平均线
            ma_5 = np.mean(prices[-5:])
            ma_10 = np.mean(prices[-10:])
            ma_20 = np.mean(prices[-20:]) if len(prices) >= 20 else ma_10
            
            # 判断趋势方向
            current_price = prices[-1]
            
            if current_price > ma_5 > ma_10 > ma_20:
                trend_direction = "upward"
                trend_strength = min(1.0, (current_price - ma_20) / ma_20 / 0.1)
            elif current_price < ma_5 < ma_10 < ma_20:
                trend_direction = "downward"
                trend_strength = min(1.0, (ma_20 - current_price) / ma_20 / 0.1)
            else:
                trend_direction = "sideways"
                trend_strength = 0.3
            
            # 分析关键因素
            key_factors = []
            
            # 成交量分析
            recent_volume = np.mean(volumes[-5:])
            avg_volume = np.mean(volumes)
            
            if recent_volume > avg_volume * 1.5:
                key_factors.append("成交量放大")
            elif recent_volume < avg_volume * 0.5:
                key_factors.append("成交量萎缩")
            
            # 价格波动分析
            price_changes = [(prices[i] - prices[i-1]) / prices[i-1] for i in range(1, len(prices))]
            volatility = np.std(price_changes[-10:])
            
            if volatility > 0.03:
                key_factors.append("高波动率")
                risk_level = "high"
            elif volatility > 0.015:
                key_factors.append("中等波动率")
                risk_level = "medium"
            else:
                key_factors.append("低波动率")
                risk_level = "low"
            
            # 计算置信度
            trend_consistency = self._calculate_trend_consistency(prices)
            volume_confirmation = 1.0 if (trend_direction == "upward" and recent_volume > avg_volume) or \
                                        (trend_direction == "downward" and recent_volume > avg_volume) else 0.7
            
            confidence = (trend_consistency * 0.7 + volume_confirmation * 0.3) * trend_strength
            
            # 预测趋势持续时间
            if trend_strength > 0.7:
                trend_duration = 72  # 3天
            elif trend_strength > 0.4:
                trend_duration = 24  # 1天
            else:
                trend_duration = 4   # 4小时
            
            trend_analysis = TrendAnalysis(
                trend_id=f"trend_{target}_{datetime.now().strftime('%Y%m%d_%H%M%S')}",
                target=target,
                trend_direction=trend_direction,
                trend_strength=trend_strength,
                trend_duration=trend_duration,
                key_factors=key_factors,
                risk_level=risk_level,
                confidence=confidence,
                timestamp=datetime.now()
            )
            
            return trend_analysis
            
        except Exception as e:
            logger.error(f"执行趋势分析失败: {e}")
            return None
    
    def _calculate_trend_consistency(self, prices: List[float]) -> float:
        """计算趋势一致性"""
        try:
            if len(prices) < 5:
                return 0.5
            
            # 计算价格变化方向的一致性
            changes = [1 if prices[i] > prices[i-1] else -1 for i in range(1, len(prices))]
            
            # 计算最近10个变化的一致性
            recent_changes = changes[-10:] if len(changes) >= 10 else changes
            
            if not recent_changes:
                return 0.5
            
            # 计算同方向变化的比例
            positive_changes = sum(1 for c in recent_changes if c > 0)
            negative_changes = sum(1 for c in recent_changes if c < 0)
            
            consistency = max(positive_changes, negative_changes) / len(recent_changes)
            
            return consistency
            
        except Exception as e:
            logger.error(f"计算趋势一致性失败: {e}")
            return 0.5
    
    async def _get_historical_data(self, stock_code: str, days: int = 30) -> List[Dict]:
        """获取历史数据 - 优先使用本地数据库"""
        try:
            # 1. 首先尝试从本地股票数据库获取
            local_data = await self._get_local_historical_data(stock_code, days)
            if local_data:
                logger.info(f"✅ 从本地数据库获取 {stock_code} 历史数据: {len(local_data)} 条")
                return local_data

            # 2. 如果本地没有数据，尝试使用统一财经API
            try:
                from services.data.unified_financial_api import UnifiedFinancialAPI, DataSource

                async with UnifiedFinancialAPI() as api:
                    # 获取K线数据
                    kline_data = await api.get_kline_data(
                        stock_code=stock_code,
                        period="daily",
                        count=days,
                        source=DataSource.EASTMONEY
                    )

                    if kline_data and kline_data.get('success'):
                        raw_data = kline_data.get('data', [])

                        # 转换为标准格式
                        historical_data = []
                        for item in raw_data:
                            try:
                                historical_data.append({
                                    'date': datetime.fromisoformat(item.get('date', datetime.now().isoformat())),
                                    'price': float(item.get('close', 0)),
                                    'volume': float(item.get('volume', 0)),
                                    'high': float(item.get('high', 0)),
                                    'low': float(item.get('low', 0)),
                                    'open': float(item.get('open', 0)),
                                    'close': float(item.get('close', 0))
                                })
                            except (ValueError, TypeError) as e:
                                logger.warning(f"数据转换失败: {e}")
                                continue

                        if historical_data:
                            logger.info(f"✅ 从API获取 {stock_code} 历史数据: {len(historical_data)} 条")
                            return historical_data

            except Exception as api_error:
                logger.warning(f"API获取数据失败: {api_error}")

            # 3. 如果都失败了，返回空列表
            logger.warning(f"⚠️ 无法获取 {stock_code} 的历史数据")
            return []

        except Exception as e:
            logger.error(f"获取历史数据失败: {e}")
            return []

    async def _get_local_historical_data(self, stock_code: str, days: int = 30) -> List[Dict]:
        """直接从数据库获取历史数据 - 不再通过瑶光星"""
        try:
            # 直接使用真实数据库服务，不再通过瑶光星
            return await self._get_data_from_real_database(stock_code, days)

        except Exception as e:
            logger.error(f"获取本地历史数据失败: {e}")
            return []

    # 移除瑶光星相关方法 - 不再通过瑶光星获取历史数据

    async def _get_data_from_real_database(self, stock_code: str, days: int = 30) -> List[Dict]:
        """从真实数据库服务获取数据"""
        try:
            from shared.database.real_database_service import real_database_service

            logger.info(f"📊 从真实数据库获取历史数据: {stock_code}")

            historical_data = await real_database_service.get_stock_historical_data(stock_code, days)

            if historical_data:
                logger.info(f"✅ 真实数据库返回数据: {len(historical_data)} 条")
                return historical_data
            else:
                logger.warning(f"⚠️ 真实数据库无数据: {stock_code}")
                return []

        except Exception as e:
            logger.error(f"真实数据库服务失败: {e}")
            return []

    async def _direct_database_access(self, stock_code: str, days: int = 30) -> List[Dict]:
        """直接访问数据库（备用方案）- 已弃用，使用真实数据库服务"""
        logger.warning("直接数据库访问已弃用，请使用真实数据库服务")
        return await self._get_data_from_real_database(stock_code, days)


    
    async def _extract_all_features(self, historical_data: List[Dict]) -> Dict[str, Any]:
        """提取所有特征"""
        features = {}
        
        try:
            for feature_type, extractor in self.feature_extractors.items():
                feature_data = await extractor(historical_data)
                features.update(feature_data)
            
            return features
            
        except Exception as e:
            logger.error(f"特征提取失败: {e}")
            return {}
    
    async def _extract_price_features(self, historical_data: List[Dict]) -> Dict[str, Any]:
        """提取价格特征"""
        try:
            prices = [d['price'] for d in historical_data]
            
            features = {
                'current_price': prices[-1],
                'price_ma_5': np.mean(prices[-5:]),
                'price_ma_10': np.mean(prices[-10:]) if len(prices) >= 10 else np.mean(prices),
                'price_change_1d': (prices[-1] - prices[-2]) / prices[-2] if len(prices) >= 2 else 0,
                'price_change_5d': (prices[-1] - prices[-6]) / prices[-6] if len(prices) >= 6 else 0,
                'price_volatility': np.std(prices[-10:]) if len(prices) >= 10 else 0
            }
            
            return features
            
        except Exception as e:
            logger.error(f"提取价格特征失败: {e}")
            return {}
    
    async def _extract_volume_features(self, historical_data: List[Dict]) -> Dict[str, Any]:
        """提取成交量特征"""
        try:
            volumes = [d['volume'] for d in historical_data]
            
            features = {
                'current_volume': volumes[-1],
                'volume_ma_5': np.mean(volumes[-5:]),
                'volume_ma_10': np.mean(volumes[-10:]) if len(volumes) >= 10 else np.mean(volumes),
                'volume_change_1d': (volumes[-1] - volumes[-2]) / volumes[-2] if len(volumes) >= 2 else 0,
                'volume_ratio': volumes[-1] / np.mean(volumes) if np.mean(volumes) > 0 else 1
            }
            
            return features
            
        except Exception as e:
            logger.error(f"提取成交量特征失败: {e}")
            return {}
    
    async def _extract_technical_features(self, historical_data: List[Dict]) -> Dict[str, Any]:
        """提取技术指标特征"""
        try:
            prices = [d['price'] for d in historical_data]
            
            # RSI计算
            rsi = self._calculate_rsi(prices)
            
            # MACD计算
            macd = self._calculate_macd(prices)
            
            features = {
                'rsi': rsi,
                'macd': macd,
                'price_position': (prices[-1] - min(prices[-20:])) / (max(prices[-20:]) - min(prices[-20:])) if len(prices) >= 20 else 0.5
            }
            
            return features
            
        except Exception as e:
            logger.error(f"提取技术特征失败: {e}")
            return {}
    
    def _calculate_rsi(self, prices: List[float], period: int = 14) -> float:
        """计算RSI指标"""
        try:
            if len(prices) < period + 1:
                return 50.0
            
            changes = [prices[i] - prices[i-1] for i in range(1, len(prices))]
            gains = [c if c > 0 else 0 for c in changes]
            losses = [-c if c < 0 else 0 for c in changes]
            
            avg_gain = np.mean(gains[-period:])
            avg_loss = np.mean(losses[-period:])
            
            if avg_loss == 0:
                return 100.0
            
            rs = avg_gain / avg_loss
            rsi = 100 - (100 / (1 + rs))
            
            return rsi
            
        except Exception as e:
            logger.error(f"计算RSI失败: {e}")
            return 50.0
    
    def _calculate_macd(self, prices: List[float]) -> float:
        """计算MACD指标"""
        try:
            if len(prices) < 26:
                return 0.0
            
            ema_12 = self._calculate_ema(prices, 12)
            ema_26 = self._calculate_ema(prices, 26)
            
            macd = ema_12 - ema_26
            
            return macd
            
        except Exception as e:
            logger.error(f"计算MACD失败: {e}")
            return 0.0
    
    def _calculate_ema(self, prices: List[float], period: int) -> float:
        """计算指数移动平均"""
        try:
            if len(prices) < period:
                return np.mean(prices)
            
            multiplier = 2 / (period + 1)
            ema = prices[0]
            
            for price in prices[1:]:
                ema = (price * multiplier) + (ema * (1 - multiplier))
            
            return ema
            
        except Exception as e:
            logger.error(f"计算EMA失败: {e}")
            return np.mean(prices)
    
    async def _extract_sentiment_features(self, historical_data: List[Dict]) -> Dict[str, Any]:
        """提取情绪特征"""
        # 简化实现
        return {
            'market_sentiment': 0.5,  # 中性
            'news_sentiment': 0.5
        }
    
    async def _extract_macro_features(self, historical_data: List[Dict]) -> Dict[str, Any]:
        """提取宏观特征"""
        # 简化实现
        return {
            'market_trend': 'neutral',
            'sector_performance': 0.0
        }
    
    async def get_prediction_insights(self) -> Dict[str, Any]:
        """获取预测洞察"""
        try:
            insights = {
                "prediction_stats": self.prediction_stats,
                "recent_predictions": len([p for p in self.prediction_history if 
                                         (datetime.now() - p.timestamp).hours < 24]),
                "active_trends": len(self.trend_analysis_cache),
                "model_status": {model: "active" for model in self.models.keys()},
                "top_predictions": []
            }
            
            # 获取最佳预测
            recent_predictions = [p for p in self.prediction_history if 
                                (datetime.now() - p.timestamp).hours < 24]
            
            top_predictions = sorted(recent_predictions, key=lambda p: p.confidence, reverse=True)[:5]
            
            insights["top_predictions"] = [
                {
                    "prediction_id": p.prediction_id,
                    "target": p.target,
                    "prediction_type": p.prediction_type,
                    "confidence": p.confidence,
                    "prediction_value": p.prediction_value
                }
                for p in top_predictions
            ]
            
            return insights

        except Exception as e:
            logger.error(f"获取预测洞察失败: {e}")
            return {"error": str(e)}

    # ==================== 缺失的预测方法实现 ====================

    async def predict_market_trends(self, prediction_data: Dict[str, Any]) -> Dict[str, Any]:
        """预测市场趋势 - 主要接口方法"""
        try:
            stock_code = prediction_data.get("stock_code", "000001")
            historical_data = prediction_data.get("historical_data", [])
            prediction_horizon = prediction_data.get("prediction_horizon", 5)

            logger.info(f"🔮 开始预测市场趋势: {stock_code}")

            # 执行多种预测
            price_prediction = await self.predict_price_trend(stock_code, prediction_horizon * 24)
            volume_prediction = await self.predict_volume_forecast(stock_code, prediction_horizon * 24)
            volatility_prediction = await self.predict_volatility(stock_code, prediction_horizon * 24)

            # 组合预测结果
            predictions = []

            if price_prediction:
                predictions.append({
                    "target": "价格趋势",
                    "prediction": price_prediction.prediction_value,
                    "confidence": price_prediction.confidence,
                    "direction": "上涨" if price_prediction.prediction_value > 0 else "下跌"
                })

            if volume_prediction:
                predictions.append({
                    "target": "成交量",
                    "prediction": volume_prediction.prediction_value,
                    "confidence": volume_prediction.confidence,
                    "trend": "放大" if volume_prediction.prediction_value > 1 else "缩小"
                })

            if volatility_prediction:
                predictions.append({
                    "target": "波动率",
                    "prediction": volatility_prediction.prediction_value,
                    "confidence": volatility_prediction.confidence,
                    "level": "高" if volatility_prediction.prediction_value > 0.03 else "低"
                })

            return {
                "success": True,
                "predictions": predictions,
                "prediction_count": len(predictions),
                "overall_confidence": sum(p["confidence"] for p in predictions) / len(predictions) if predictions else 0
            }

        except Exception as e:
            logger.error(f"预测市场趋势失败: {e}")
            return {
                "success": False,
                "error": str(e)
            }

    async def _select_best_model(self, prediction_type: str, features: Dict[str, Any]) -> str:
        """选择最佳预测模型"""
        try:
            # 基于特征数量和预测类型选择模型
            feature_count = len(features)

            if prediction_type == "price_prediction":
                if feature_count >= 10:
                    return "ensemble"  # 特征丰富时使用集成模型
                elif feature_count >= 5:
                    return "random_forest"
                else:
                    return "linear_regression"

            elif prediction_type == "volume_prediction":
                return "random_forest"  # 成交量预测适合随机森林

            elif prediction_type == "volatility_prediction":
                return "statistical_model"  # 波动率使用统计模型

            else:
                return "linear_regression"  # 默认线性回归

        except Exception as e:
            logger.error(f"选择预测模型失败: {e}")
            return "linear_regression"

    async def _execute_prediction(self, model_name: str, features: Dict[str, Any],
                                prediction_target: str) -> float:
        """执行预测"""
        try:
            if model_name == "linear_regression":
                return await self._linear_regression_predict(features, prediction_target)
            elif model_name == "random_forest":
                return await self._random_forest_predict(features, prediction_target)
            elif model_name == "ensemble":
                return await self._ensemble_predict(features, prediction_target)
            elif model_name == "statistical_model":
                return await self._statistical_predict(features, prediction_target)
            else:
                # 默认简单预测
                return await self._simple_predict(features, prediction_target)

        except Exception as e:
            logger.error(f"执行预测失败: {e}")
            return 0.0

    async def _calculate_prediction_confidence(self, model_name: str, features: Dict[str, Any],
                                             historical_data: List[Dict]) -> float:
        """计算预测置信度"""
        try:
            # 基于数据质量和模型特性计算置信度
            data_quality = min(1.0, len(historical_data) / 30)  # 数据充足度
            feature_quality = min(1.0, len(features) / 10)      # 特征丰富度

            # 模型基础置信度
            model_confidence = {
                "ensemble": 0.85,
                "random_forest": 0.75,
                "linear_regression": 0.65,
                "statistical_model": 0.70
            }.get(model_name, 0.60)

            # 综合置信度
            confidence = model_confidence * data_quality * feature_quality

            return max(0.3, min(0.95, confidence))

        except Exception as e:
            logger.error(f"计算预测置信度失败: {e}")
            return 0.5

    async def _linear_regression_predict(self, features: Dict[str, Any], target: str) -> float:
        """线性回归预测"""
        try:
            # 提取数值特征
            numeric_features = []
            for key, value in features.items():
                if isinstance(value, (int, float)):
                    numeric_features.append(value)

            if len(numeric_features) < 2:
                return 0.0

            # 简单的线性组合预测
            weights = [0.3, 0.2, 0.15, 0.1, 0.05]  # 递减权重
            prediction = 0.0

            for i, feature in enumerate(numeric_features[:5]):
                weight = weights[i] if i < len(weights) else 0.05
                prediction += feature * weight

            # 标准化到合理范围
            return max(-0.2, min(0.2, prediction / 100))

        except Exception as e:
            logger.error(f"线性回归预测失败: {e}")
            return 0.0

    async def _random_forest_predict(self, features: Dict[str, Any], target: str) -> float:
        """随机森林预测"""
        try:
            # 模拟随机森林的多树投票
            predictions = []

            for tree_id in range(5):  # 5棵树
                tree_prediction = await self._single_tree_predict(features, tree_id)
                predictions.append(tree_prediction)

            # 平均预测结果
            return np.mean(predictions)

        except Exception as e:
            logger.error(f"随机森林预测失败: {e}")
            return 0.0

    async def _single_tree_predict(self, features: Dict[str, Any], tree_id: int) -> float:
        """单棵决策树预测"""
        try:
            # 简化的决策树逻辑
            price_change = features.get('price_change_1d', 0)
            volume_ratio = features.get('volume_ratio', 1)
            rsi = features.get('rsi', 50)

            # 简单的决策规则
            if price_change > 0.03 and volume_ratio > 1.5:
                return 0.05 + np.random.normal(0, 0.01)  # 看涨
            elif price_change < -0.03 and volume_ratio > 1.5:
                return -0.05 + np.random.normal(0, 0.01)  # 看跌
            elif rsi > 70:
                return -0.02 + np.random.normal(0, 0.005)  # 超买
            elif rsi < 30:
                return 0.02 + np.random.normal(0, 0.005)  # 超卖
            else:
                return np.random.normal(0, 0.01)  # 中性

        except Exception as e:
            logger.error(f"决策树预测失败: {e}")
            return 0.0

    async def _ensemble_predict(self, features: Dict[str, Any], target: str) -> float:
        """集成模型预测"""
        try:
            # 组合多个模型的预测结果
            lr_pred = await self._linear_regression_predict(features, target)
            rf_pred = await self._random_forest_predict(features, target)
            stat_pred = await self._statistical_predict(features, target)

            # 加权平均
            ensemble_pred = (lr_pred * 0.3 + rf_pred * 0.5 + stat_pred * 0.2)

            return ensemble_pred

        except Exception as e:
            logger.error(f"集成模型预测失败: {e}")
            return 0.0

    async def _statistical_predict(self, features: Dict[str, Any], target: str) -> float:
        """统计模型预测"""
        try:
            # 基于统计指标的预测
            volatility = features.get('price_volatility', 0.02)
            ma_5 = features.get('price_ma_5', 0)
            ma_10 = features.get('price_ma_10', 0)
            current_price = features.get('current_price', 0)

            if ma_5 > 0 and ma_10 > 0 and current_price > 0:
                # 基于移动平均的趋势预测
                ma_trend = (ma_5 - ma_10) / ma_10
                price_position = (current_price - ma_10) / ma_10

                # 结合波动率调整预测
                prediction = ma_trend * 0.5 + price_position * 0.3

                # 波动率调整
                if volatility > 0.05:  # 高波动率
                    prediction *= 0.7  # 降低预测幅度

                return max(-0.15, min(0.15, prediction))

            return 0.0

        except Exception as e:
            logger.error(f"统计模型预测失败: {e}")
            return 0.0

    async def _simple_predict(self, features: Dict[str, Any], target: str) -> float:
        """简单预测"""
        try:
            # 基于最基本的特征进行预测
            price_change = features.get('price_change_1d', 0)
            volume_change = features.get('volume_change_1d', 0)

            # 简单的动量预测
            momentum = price_change * 0.5

            # 成交量确认
            if abs(volume_change) > 0.5:  # 成交量显著变化
                momentum *= 1.2  # 增强信号

            return max(-0.1, min(0.1, momentum))

        except Exception as e:
            logger.error(f"简单预测失败: {e}")
            return 0.0

# 全局实例
predictive_analysis_engine = PredictiveAnalysisEngine()
