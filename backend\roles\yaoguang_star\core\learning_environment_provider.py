#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
学习环境提供者
为各角色提供隔离的学习环境，确保学习效果和资源管理
"""

import asyncio
import uuid
from datetime import datetime
from typing import Dict, List, Optional, Any
from dataclasses import dataclass, asdict
import logging

logger = logging.getLogger(__name__)

# 临时类定义 - 解决导入问题
class ResourcePool:
    """资源池管理器"""

    def __init__(self):
        self.resources = {}
        self.allocated_resources = {}

    async def allocate_resources(self, env_id: str, role_id: str, requirements: Dict) -> Dict:
        """分配资源"""
        allocation = {
            "cpu_cores": requirements.get("cpu_cores", 2),
            "memory_gb": requirements.get("memory_gb", 4.0),
            "storage_gb": requirements.get("storage_gb", 10.0),
            "gpu_allocation": requirements.get("gpu_allocation"),
            "network_bandwidth": requirements.get("network_bandwidth", "1Gbps")
        }
        self.allocated_resources[env_id] = allocation
        return allocation

    async def update_allocation(self, env_id: str, updates: Dict):
        """更新资源分配"""
        if env_id in self.allocated_resources:
            self.allocated_resources[env_id].update(updates)

    async def release_resources(self, env_id: str):
        """释放资源"""
        if env_id in self.allocated_resources:
            del self.allocated_resources[env_id]

class IsolationController:
    """隔离控制器"""

    def __init__(self):
        self.isolation_configs = {}

    async def create_isolation(self, env_id: str, role_id: str, isolation_level: str) -> Dict:
        """创建隔离环境"""
        config = {
            "isolation_level": isolation_level,
            "data_permissions": {
                "allowed_data_types": ["market_data", "news_data", "fundamental_data"],
                "allowed_stocks": []  # 空列表表示允许所有股票
            },
            "network_isolation": isolation_level == "high",
            "file_system_isolation": True
        }
        self.isolation_configs[env_id] = config
        return config

    async def update_isolation(self, env_id: str, updates: Dict):
        """更新隔离配置"""
        if env_id in self.isolation_configs:
            self.isolation_configs[env_id].update(updates)

    async def cleanup_isolation(self, env_id: str):
        """清理隔离环境"""
        if env_id in self.isolation_configs:
            del self.isolation_configs[env_id]

class DataQualityValidator:
    """数据质量验证器"""

    def __init__(self):
        self.validation_rules = {}

    async def validate(self, data: Any) -> 'QualityCheckResult':
        """验证数据质量"""
        return QualityCheckResult(is_valid=True, issues=[])

class QualityCheckResult:
    """质量检查结果"""

    def __init__(self, is_valid: bool, issues: List[str]):
        self.is_valid = is_valid
        self.issues = issues

class TimeViolationError(Exception):
    """时间违规错误"""
    pass

@dataclass
class LearningEnvironment:
    """学习环境数据结构"""
    env_id: str
    role_id: str
    environment_type: str
    resource_allocation: Dict
    isolation_config: Dict
    data_config: Dict
    rd_agent_config: Dict
    status: str
    created_time: datetime
    last_used_time: Optional[datetime] = None

@dataclass
class ResourceAllocation:
    """资源分配"""
    cpu_cores: int
    memory_gb: float
    storage_gb: float
    gpu_allocation: Optional[str]
    network_bandwidth: str

@dataclass
class DataRequest:
    """数据请求"""
    request_id: str
    env_id: str
    data_type: str
    stock_codes: List[str]
    date_range: Dict
    additional_params: Dict

class PermissionError(Exception):
    """权限错误"""
    pass

class DataQualityError(Exception):
    """数据质量错误"""
    pass

class LearningEnvironmentProvider:
    """学习环境提供者"""
    
    def __init__(self):
        self.environments: Dict[str, LearningEnvironment] = {}
        self.resource_pool = ResourcePool()
        self.isolation_controller = IsolationController()
        self.data_quality_validator = DataQualityValidator()
        
        # RD-Agent集成
        self.rd_agent_env_manager = None  # 延迟初始化
        
        logger.info("学习环境提供者初始化完成")
    
    async def create_isolated_environment(self, 
                                        role_id: str,
                                        environment_type: str,
                                        learning_config: Dict) -> str:
        """为角色创建隔离学习环境"""
        
        env_id = f"env_{role_id}_{environment_type}_{uuid.uuid4().hex[:8]}"
        
        try:
            # 1. 资源分配
            resource_requirements = learning_config.get("resources", {})
            allocated_resources = await self.resource_pool.allocate_resources(
                env_id=env_id,
                role_id=role_id,
                requirements=resource_requirements
            )
            
            # 2. 环境隔离配置
            isolation_config = await self.isolation_controller.create_isolation(
                env_id=env_id,
                role_id=role_id,
                isolation_level=learning_config.get("isolation_level", "standard")
            )
            
            # 3. 数据环境配置
            data_config = await self._prepare_data_environment(
                env_id, learning_config.get("data_config", {})
            )
            
            # 4. RD-Agent环境集成
            rd_agent_config = {}
            if learning_config.get("enable_rd_agent", False):
                rd_agent_config = await self._setup_rd_agent_environment(
                    env_id=env_id,
                    role_id=role_id,
                    config=learning_config.get("rd_agent_config", {})
                )
            
            # 5. 创建环境对象
            environment = LearningEnvironment(
                env_id=env_id,
                role_id=role_id,
                environment_type=environment_type,
                resource_allocation=allocated_resources,
                isolation_config=isolation_config,
                data_config=data_config,
                rd_agent_config=rd_agent_config,
                status="active",
                created_time=datetime.now()
            )
            
            self.environments[env_id] = environment
            
            logger.info(f"创建学习环境成功: {env_id} for {role_id}")
            return env_id
            
        except Exception as e:
            logger.error(f"创建学习环境失败: {e}")
            # 清理已分配的资源
            await self._cleanup_failed_environment(env_id)
            raise
    
    async def provide_learning_data(self, env_id: str, data_request: DataRequest) -> Dict[str, Any]:
        """为学习环境提供数据"""
        
        if env_id not in self.environments:
            raise ValueError(f"学习环境不存在: {env_id}")
        
        environment = self.environments[env_id]
        
        # 1. 权限检查
        if not await self._check_data_access_permission(environment, data_request):
            raise PermissionError(f"数据访问权限不足: {env_id}")
        
        # 2. 时间约束检查
        if not await self._check_time_constraints(environment, data_request):
            raise TimeViolationError(f"时间约束违规: {env_id}")
        
        # 3. 数据准备
        learning_data = await self._prepare_learning_data(environment, data_request)
        
        # 4. 数据质量验证
        quality_check = await self.data_quality_validator.validate(learning_data)
        if not quality_check.is_valid:
            raise DataQualityError(f"数据质量不合格: {quality_check.issues}")
        
        # 5. 记录数据使用
        await self._log_data_usage(environment, data_request, learning_data)
        
        # 6. 更新环境使用时间
        environment.last_used_time = datetime.now()
        
        return {
            "data": learning_data,
            "quality_report": quality_check,
            "usage_info": {
                "request_id": data_request.request_id,
                "provided_time": datetime.now(),
                "data_size": len(learning_data) if isinstance(learning_data, (list, dict)) else 0
            }
        }
    
    async def update_environment_config(self, env_id: str, config_updates: Dict) -> bool:
        """更新环境配置"""
        
        if env_id not in self.environments:
            raise ValueError(f"学习环境不存在: {env_id}")
        
        environment = self.environments[env_id]
        
        try:
            # 更新资源配置
            if "resources" in config_updates:
                await self.resource_pool.update_allocation(
                    env_id, config_updates["resources"]
                )
                environment.resource_allocation.update(config_updates["resources"])
            
            # 更新隔离配置
            if "isolation" in config_updates:
                await self.isolation_controller.update_isolation(
                    env_id, config_updates["isolation"]
                )
                environment.isolation_config.update(config_updates["isolation"])
            
            # 更新数据配置
            if "data_config" in config_updates:
                environment.data_config.update(config_updates["data_config"])
            
            # 更新RD-Agent配置
            if "rd_agent_config" in config_updates:
                environment.rd_agent_config.update(config_updates["rd_agent_config"])
            
            logger.info(f"环境配置更新成功: {env_id}")
            return True
            
        except Exception as e:
            logger.error(f"环境配置更新失败: {e}")
            return False
    
    async def destroy_environment(self, env_id: str) -> bool:
        """销毁学习环境"""
        
        if env_id not in self.environments:
            return True  # 已经不存在
        
        environment = self.environments[env_id]
        
        try:
            # 1. 停止所有运行中的任务
            await self._stop_environment_tasks(env_id)
            
            # 2. 释放资源
            await self.resource_pool.release_resources(env_id)
            
            # 3. 清理隔离环境
            await self.isolation_controller.cleanup_isolation(env_id)
            
            # 4. 清理RD-Agent环境
            if environment.rd_agent_config:
                await self._cleanup_rd_agent_environment(env_id)
            
            # 5. 从环境列表中移除
            del self.environments[env_id]
            
            logger.info(f"学习环境销毁成功: {env_id}")
            return True
            
        except Exception as e:
            logger.error(f"学习环境销毁失败: {e}")
            return False
    
    async def _prepare_data_environment(self, env_id: str, data_config: Dict) -> Dict:
        """准备数据环境"""
        
        prepared_config = {
            "data_sources": data_config.get("data_sources", ["akshare", "tushare"]),
            "cache_config": {
                "enable_cache": True,
                "cache_size_mb": data_config.get("cache_size_mb", 1024),
                "cache_ttl_hours": data_config.get("cache_ttl_hours", 24)
            },
            "data_quality": {
                "enable_validation": True,
                "missing_data_threshold": 0.05,
                "outlier_detection": True
            }
        }
        
        return prepared_config
    
    async def _setup_rd_agent_environment(self, env_id: str, role_id: str, config: Dict) -> Dict:
        """设置RD-Agent环境"""
        
        rd_agent_config = {
            "experiment_config": {
                "max_experiments": config.get("max_experiments", 10),
                "parallel_experiments": config.get("parallel_experiments", 2),
                "experiment_timeout_hours": config.get("experiment_timeout_hours", 24)
            },
            "factor_generation": {
                "enable_auto_generation": config.get("enable_auto_generation", True),
                "max_factors_per_experiment": config.get("max_factors_per_experiment", 50),
                "factor_validation_threshold": config.get("factor_validation_threshold", 0.02)
            },
            "model_research": {
                "enable_model_research": config.get("enable_model_research", True),
                "model_types": config.get("model_types", ["linear", "tree", "neural"]),
                "hyperparameter_optimization": config.get("hyperparameter_optimization", True)
            }
        }
        
        return rd_agent_config
    
    async def _check_data_access_permission(self, environment: LearningEnvironment, data_request: DataRequest) -> bool:
        """检查数据访问权限"""
        
        # 检查角色权限
        role_permissions = environment.isolation_config.get("data_permissions", {})
        requested_data_type = data_request.data_type
        
        if requested_data_type not in role_permissions.get("allowed_data_types", []):
            return False
        
        # 检查股票代码权限
        allowed_stocks = role_permissions.get("allowed_stocks", [])
        if allowed_stocks and not all(code in allowed_stocks for code in data_request.stock_codes):
            return False
        
        return True
    
    async def _check_time_constraints(self, environment: LearningEnvironment, data_request: DataRequest) -> bool:
        """检查时间约束"""
        
        # 这里应该与时间控制引擎集成
        # 返回真实数据
        return True

    async def _prepare_learning_data(self, environment: LearningEnvironment, data_request: DataRequest) -> Dict:
        """准备真实学习数据"""
        try:
            from .real_data_manager import real_data_manager

            # 获取真实的股票数据
            stock_data = []
            if data_request.stock_codes:
                for stock_code in data_request.stock_codes:
                    data = await real_data_manager.get_real_stock_data(
                        stock_code, data_request.start_date, data_request.end_date
                    )
                    if not data.empty:
                        stock_data.append({
                            "stock_code": stock_code,
                            "data": data.to_dict('records')
                        })

            # 获取真实的基本面数据
            fundamental_data = {}
            if data_request.stock_codes:
                for stock_code in data_request.stock_codes:
                    stock_list = await real_data_manager.get_real_stock_list(limit=1000)
                    stock_info = next((s for s in stock_list if s.get('stock_code') == stock_code), {})
                    if stock_info:
                        fundamental_data[stock_code] = stock_info

            return {
                "stock_data": stock_data,
                "market_data": {"data_source": "real_database"},
                "news_data": [],  # 新闻数据由天枢星负责
                "fundamental_data": fundamental_data
            }

        except Exception as e:
            logger.error(f"准备学习数据失败: {e}")
            return {
                "stock_data": [],
                "market_data": {},
                "news_data": [],
                "fundamental_data": {},
                "error": str(e)
            }

    async def _log_data_usage(self, environment: LearningEnvironment, data_request: DataRequest, learning_data: Dict):
        """记录数据使用"""
        logger.info(f"数据使用记录: {environment.env_id} - {data_request.request_id}")

    async def _cleanup_failed_environment(self, env_id: str):
        """清理失败的环境"""
        logger.info(f"清理失败环境: {env_id}")

    async def _stop_environment_tasks(self, env_id: str):
        """停止环境任务"""
        logger.info(f"停止环境任务: {env_id}")

    async def _cleanup_rd_agent_environment(self, env_id: str):
        """清理RD-Agent环境"""
        logger.info(f"清理RD-Agent环境: {env_id}")