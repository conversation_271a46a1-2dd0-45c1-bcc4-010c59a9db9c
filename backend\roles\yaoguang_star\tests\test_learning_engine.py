#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
瑶光星学习引擎单元测试
"""

import unittest
import asyncio
from datetime import datetime, timedelta
from unittest.mock import Mock, patch, AsyncMock
import sys
import os

# 添加项目路径
sys.path.append(os.path.dirname(os.path.dirname(os.path.dirname(os.path.dirname(__file__)))))

from roles.yaoguang_star.core.real_learning_process_engine import RealLearningProcessEngine, LearningCycle

class TestRealLearningProcessEngine(unittest.TestCase):
    """真实学习过程引擎测试"""
    
    def setUp(self):
        """测试前准备"""
        self.learning_engine = RealLearningProcessEngine()
    
    def test_init(self):
        """测试初始化"""
        self.assertEqual(self.learning_engine.engine_name, "瑶光星真实学习过程引擎")
        self.assertEqual(self.learning_engine.version, "1.0.0")
        self.assertIsInstance(self.learning_engine.active_cycles, dict)
        self.assertIsInstance(self.learning_engine.learning_history, list)
    
    async def test_start_learning_cycle_success(self):
        """测试成功启动学习周期"""
        config = {"test_mode": True}
        
        result = await self.learning_engine.start_learning_cycle(config)
        
        self.assertTrue(result["success"])
        self.assertIn("cycle_id", result)
        self.assertIn("start_time", result)
        self.assertEqual(result["config"], config)
        
        # 验证周期已添加到活跃周期中
        cycle_id = result["cycle_id"]
        self.assertIn(cycle_id, self.learning_engine.active_cycles)
    
    async def test_start_learning_cycle_max_limit(self):
        """测试达到最大并发周期限制"""
        # 先创建最大数量的周期
        for i in range(self.learning_engine.max_concurrent_cycles):
            await self.learning_engine.start_learning_cycle({"test": i})
        
        # 尝试创建超出限制的周期
        result = await self.learning_engine.start_learning_cycle({"test": "overflow"})
        
        self.assertFalse(result["success"])
        self.assertIn("已达到最大并发学习周期数量", result["error"])
    
    async def test_get_learning_status(self):
        """测试获取学习状态"""
        # 创建一个学习周期
        await self.learning_engine.start_learning_cycle({"test": True})
        
        status = await self.learning_engine.get_learning_status()
        
        self.assertEqual(status["engine_name"], "瑶光星真实学习过程引擎")
        self.assertEqual(status["version"], "1.0.0")
        self.assertGreaterEqual(status["active_cycles"], 1)
        self.assertIn("last_update", status)
    
    async def test_execute_practice_phase(self):
        """测试执行练习阶段"""
        # 创建测试周期
        cycle = LearningCycle(
            cycle_id="test_cycle",
            cycle_type="practice_test",
            config={"test": True},
            start_time=datetime.now(),
            current_phase="practice",
            status="active"
        )
        
        with patch.object(self.learning_engine, '_execute_practice_phase') as mock_practice:
            mock_practice.return_value = {
                "success": True,
                "phase": "practice",
                "duration": "2.5s",
                "achievements": ["测试练习完成"],
                "next_phase": "research"
            }
            
            result = await self.learning_engine._execute_practice_phase(cycle)
            
            self.assertTrue(result["success"])
            self.assertEqual(result["phase"], "practice")
            self.assertIn("achievements", result)
    
    async def test_execute_research_phase(self):
        """测试执行研究阶段"""
        cycle = LearningCycle(
            cycle_id="test_cycle",
            cycle_type="research_test",
            config={"test": True},
            start_time=datetime.now(),
            current_phase="research",
            status="active"
        )
        
        with patch.object(self.learning_engine, '_execute_research_phase') as mock_research:
            mock_research.return_value = {
                "success": True,
                "phase": "research",
                "duration": "3.2s",
                "discoveries": ["新的交易模式", "风险控制策略"],
                "next_phase": "validation"
            }
            
            result = await self.learning_engine._execute_research_phase(cycle)
            
            self.assertTrue(result["success"])
            self.assertEqual(result["phase"], "research")
            self.assertIn("discoveries", result)
    
    async def test_execute_validation_phase(self):
        """测试执行验证阶段"""
        cycle = LearningCycle(
            cycle_id="test_cycle",
            cycle_type="validation_test",
            config={"test": True},
            start_time=datetime.now(),
            current_phase="validation",
            status="active"
        )
        
        with patch.object(self.learning_engine, '_execute_validation_phase') as mock_validation:
            mock_validation.return_value = {
                "success": True,
                "phase": "validation",
                "duration": "1.8s",
                "validation_results": {"accuracy": 0.85, "precision": 0.82},
                "next_phase": "optimization"
            }
            
            result = await self.learning_engine._execute_validation_phase(cycle)
            
            self.assertTrue(result["success"])
            self.assertEqual(result["phase"], "validation")
            self.assertIn("validation_results", result)
    
    async def test_complete_learning_cycle(self):
        """测试完成学习周期"""
        # 启动一个周期
        start_result = await self.learning_engine.start_learning_cycle({"test": True})
        cycle_id = start_result["cycle_id"]
        
        # 完成周期
        result = await self.learning_engine.complete_learning_cycle(cycle_id)
        
        self.assertTrue(result["success"])
        self.assertEqual(result["cycle_id"], cycle_id)
        self.assertIn("total_duration", result)
        
        # 验证周期已从活跃周期中移除
        self.assertNotIn(cycle_id, self.learning_engine.active_cycles)
        
        # 验证周期已添加到历史中
        self.assertGreater(len(self.learning_engine.learning_history), 0)
    
    async def test_complete_nonexistent_cycle(self):
        """测试完成不存在的学习周期"""
        result = await self.learning_engine.complete_learning_cycle("nonexistent_cycle")
        
        self.assertFalse(result["success"])
        self.assertIn("学习周期不存在", result["error"])
    
    async def test_get_learning_history(self):
        """测试获取学习历史"""
        # 创建并完成几个周期
        for i in range(3):
            start_result = await self.learning_engine.start_learning_cycle({"test": i})
            cycle_id = start_result["cycle_id"]
            await self.learning_engine.complete_learning_cycle(cycle_id)
        
        history = await self.learning_engine.get_learning_history(limit=2)
        
        self.assertLessEqual(len(history), 2)
        if history:
            self.assertIn("cycle_id", history[0])
            self.assertIn("cycle_type", history[0])
            self.assertIn("duration", history[0])

class TestLearningCycle(unittest.TestCase):
    """学习周期数据类测试"""
    
    def test_learning_cycle_creation(self):
        """测试学习周期创建"""
        start_time = datetime.now()
        cycle = LearningCycle(
            cycle_id="test_cycle_001",
            cycle_type="comprehensive",
            config={"learning_rate": 0.1},
            start_time=start_time,
            current_phase="practice",
            status="active"
        )
        
        self.assertEqual(cycle.cycle_id, "test_cycle_001")
        self.assertEqual(cycle.cycle_type, "comprehensive")
        self.assertEqual(cycle.config["learning_rate"], 0.1)
        self.assertEqual(cycle.start_time, start_time)
        self.assertEqual(cycle.current_phase, "practice")
        self.assertEqual(cycle.status, "active")
        self.assertIsNone(cycle.end_time)
        self.assertIsInstance(cycle.phase_results, dict)

class TestLearningEngineIntegration(unittest.TestCase):
    """学习引擎集成测试"""
    
    def setUp(self):
        """测试前准备"""
        self.learning_engine = RealLearningProcessEngine()
    
    async def test_full_learning_workflow(self):
        """测试完整学习工作流"""
        # 1. 启动学习周期
        config = {
            "learning_type": "comprehensive",
            "target_accuracy": 0.85,
            "max_iterations": 10
        }
        
        start_result = await self.learning_engine.start_learning_cycle(config)
        self.assertTrue(start_result["success"])
        
        cycle_id = start_result["cycle_id"]
        
        # 2. 检查状态
        status = await self.learning_engine.get_learning_status()
        self.assertGreaterEqual(status["active_cycles"], 1)
        
        # 3. 完成周期
        complete_result = await self.learning_engine.complete_learning_cycle(cycle_id)
        self.assertTrue(complete_result["success"])
        
        # 4. 验证历史记录
        history = await self.learning_engine.get_learning_history(limit=1)
        self.assertEqual(len(history), 1)
        self.assertEqual(history[0]["cycle_id"], cycle_id)
    
    async def test_concurrent_learning_cycles(self):
        """测试并发学习周期"""
        cycle_ids = []
        
        # 启动多个并发周期
        for i in range(3):
            result = await self.learning_engine.start_learning_cycle({"test": i})
            self.assertTrue(result["success"])
            cycle_ids.append(result["cycle_id"])
        
        # 验证所有周期都在活跃状态
        status = await self.learning_engine.get_learning_status()
        self.assertEqual(status["active_cycles"], 3)
        
        # 完成所有周期
        for cycle_id in cycle_ids:
            result = await self.learning_engine.complete_learning_cycle(cycle_id)
            self.assertTrue(result["success"])
        
        # 验证所有周期都已完成
        final_status = await self.learning_engine.get_learning_status()
        self.assertEqual(final_status["active_cycles"], 0)

def run_async_test(coro):
    """运行异步测试的辅助函数"""
    loop = asyncio.new_event_loop()
    asyncio.set_event_loop(loop)
    try:
        return loop.run_until_complete(coro)
    finally:
        loop.close()

# 为异步测试方法添加装饰器
for cls in [TestRealLearningProcessEngine, TestLearningEngineIntegration]:
    for name, method in cls.__dict__.items():
        if name.startswith('test_') and asyncio.iscoroutinefunction(method):
            setattr(cls, name, lambda self, m=method: run_async_test(m(self)))

if __name__ == '__main__':
    unittest.main()
