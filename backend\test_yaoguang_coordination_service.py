#!/usr/bin/env python3
"""
瑶光星协调服务测试脚本
"""

import sys
import asyncio
sys.path.append('.')

async def test_coordination_service():
    """测试瑶光星协调服务"""
    try:
        from roles.yaoguang_star.yaoguang_coordination_service import yaoguang_coordination_service
        
        print('🌟 测试瑶光星协调服务...')
        print(f'服务名称: {yaoguang_coordination_service.service_name}')
        print(f'版本: {yaoguang_coordination_service.version}')
        print(f'服务类型: {yaoguang_coordination_service.service_type}')
        
        # 获取初始状态
        print('\n📊 获取协调服务状态...')
        status = await yaoguang_coordination_service.get_coordination_status()
        
        print(f'服务状态: {status["service_info"]["service_name"]} v{status["service_info"]["version"]}')
        print(f'运行时间: {status["service_info"]["uptime"]}')
        print(f'活跃协调: {status["coordination_status"]["active_coordinations"]}')
        print(f'待处理请求: {status["coordination_status"]["pending_requests"]}')
        print(f'已完成协调: {status["coordination_status"]["completed_coordinations"]}')
        
        # 测试学习协调
        print('\n🎓 测试学习协调...')
        learning_config = {
            "learning_focus": ["pattern_recognition", "risk_assessment"],
            "learning_mode": "comprehensive",
            "use_detailed_flow": True,
            "priority": "high",
            "requester": "test_script",
            "objectives": {
                "accuracy_target": 0.85,
                "risk_control": 0.10
            },
            "data_range": {
                "start_date": "2024-01-01",
                "end_date": "2024-12-31"
            }
        }
        
        learning_result = await yaoguang_coordination_service.coordinate_learning_session(learning_config)
        
        if learning_result.get('success'):
            print('✅ 学习协调成功！')
            print(f'请求ID: {learning_result["request_id"]}')
            print(f'执行时间: {learning_result["execution_time"]:.2f}秒')
            print(f'使用协调器: {learning_result["coordinator_used"]}')
            
            coord_result = learning_result['coordination_result']
            if coord_result.get('success'):
                print(f'目标数量: {coord_result.get("objectives_count", 0)}')
                
                learning_res = coord_result.get('learning_result', {})
                if learning_res.get('success'):
                    print(f'目标达成: {learning_res.get("objectives_achieved", 0)}')
                    print(f'知识获得: {learning_res.get("knowledge_gained", 0)}')
        else:
            print(f'❌ 学习协调失败: {learning_result.get("error")}')
        
        # 测试回测协调
        print('\n🔄 测试回测协调...')
        backtest_config = {
            "strategy_name": "多因子量化策略",
            "start_date": "2024-01-01",
            "end_date": "2024-12-31",
            "initial_capital": 1000000.0,
            "benchmark": "000300.XSHG",
            "universe_size": 50,
            "rebalance_frequency": "monthly",
            "commission_rate": 0.0003,
            "slippage_rate": 0.001,
            "max_position_size": 0.1,
            "priority": "medium",
            "requester": "test_script"
        }
        
        backtest_result = await yaoguang_coordination_service.coordinate_backtest_session(backtest_config)
        
        if backtest_result.get('success'):
            print('✅ 回测协调成功！')
            print(f'请求ID: {backtest_result["request_id"]}')
            print(f'执行时间: {backtest_result["execution_time"]:.2f}秒')
            print(f'使用协调器: {backtest_result["coordinator_used"]}')
            
            coord_result = backtest_result['coordination_result']
            if coord_result.get('success'):
                print(f'回测期间: {coord_result.get("backtest_period", "未知")}')
                
                backtest_res = coord_result.get('backtest_result', {})
                if backtest_res.get('success'):
                    print(f'时间线点数: {backtest_res.get("timeline_points", 0)}')
                    
                    # 显示综合报告摘要
                    comp_report = backtest_res.get('comprehensive_report', {})
                    exec_summary = comp_report.get('executive_summary', {})
                    if exec_summary:
                        key_metrics = exec_summary.get('key_metrics', {})
                        if key_metrics:
                            print(f'总收益率: {key_metrics.get("total_return", 0):.1%}')
                            print(f'夏普比率: {key_metrics.get("sharpe_ratio", 0):.2f}')
        else:
            print(f'❌ 回测协调失败: {backtest_result.get("error")}')
        
        # 测试反馈分发
        print('\n📤 测试反馈分发...')
        if learning_result.get('success'):
            feedback_config = {
                "feedback_type": "learning",
                "source_data": {
                    "session_id": learning_result["request_id"],
                    "coordination_result": learning_result["coordination_result"],
                    "session_type": "learning"
                }
            }
            
            feedback_result = await yaoguang_coordination_service.distribute_feedback(feedback_config)
            
            if feedback_result.get('success'):
                print('✅ 反馈分发成功！')
                print(f'反馈请求ID: {feedback_result["request_id"]}')
                print(f'反馈类型: {feedback_result["feedback_type"]}')
                
                feedback_res = feedback_result.get('feedback_result', {})
                if feedback_res.get('success'):
                    print(f'反馈项数: {feedback_res.get("feedback_items_generated", 0)}')
                    print(f'目标星数: {feedback_res.get("stars_targeted", 0)}')
            else:
                print(f'❌ 反馈分发失败: {feedback_result.get("error")}')
        
        # 获取最终状态
        print('\n📈 获取最终协调状态...')
        final_status = await yaoguang_coordination_service.get_coordination_status()
        
        perf_metrics = final_status["performance_metrics"]
        print(f'总协调数: {perf_metrics["total_coordinations"]}')
        print(f'成功协调数: {perf_metrics["successful_coordinations"]}')
        print(f'平均协调时间: {perf_metrics["average_coordination_time"]:.2f}秒')
        print(f'星际协作得分: {perf_metrics["star_collaboration_score"]:.2f}')
        
        session_stats = final_status["session_statistics"]
        print(f'学习会话: {session_stats["learning"]}')
        print(f'回测会话: {session_stats["backtest"]}')
        print(f'反馈会话: {session_stats["feedback"]}')
        
        # 获取协调历史
        print('\n📋 获取协调历史...')
        history = await yaoguang_coordination_service.get_coordination_history(5)
        
        print(f'历史记录数: {len(history)}')
        for i, record in enumerate(history[:3], 1):
            print(f'  {i}. {record["request_type"]} - {record["status"]} ({record["execution_time"]:.2f}s)')
        
        # 测试引擎状态
        print('\n🔧 各协调引擎状态:')
        engines = {
            "七星协调器": yaoguang_coordination_service.seven_star_coordinator,
            "学习协调器": yaoguang_coordination_service.learning_coordinator,
            "回测协调器": yaoguang_coordination_service.backtest_coordinator,
            "反馈引擎": yaoguang_coordination_service.feedback_engine
        }
        
        for name, engine in engines.items():
            print(f'  {name}: {engine.coordinator_name if hasattr(engine, "coordinator_name") else engine.engine_name} v{engine.version}')
        
        print('\n🎉 瑶光星协调服务测试完成！')
        
    except Exception as e:
        print(f'❌ 测试执行失败: {e}')
        import traceback
        traceback.print_exc()

if __name__ == "__main__":
    asyncio.run(test_coordination_service())
