#!/usr/bin/env python3
"""
七星协调引擎测试脚本
"""

import sys
import asyncio
sys.path.append('.')

async def test_coordinator():
    """测试七星协调引擎"""
    try:
        from roles.yaoguang_star.core.seven_star_coordinator import seven_star_coordinator
        
        print('🌟 测试七星协调引擎...')
        print(f'协调器名称: {seven_star_coordinator.coordinator_name}')
        print(f'版本: {seven_star_coordinator.version}')
        
        # 测试学习协调
        learning_config = {
            'learning_mode': 'comprehensive',
            'objectives': ['pattern_recognition', 'risk_assessment'],
            'selection_criteria': {
                'max_stocks': 5,
                'sectors': ['technology', 'finance']
            }
        }
        
        print('\n🎓 启动学习协调流程...')
        result = await seven_star_coordinator.start_learning_coordination(learning_config)
        
        if result.get('success'):
            print('✅ 学习协调成功！')
            print(f'会话ID: {result["session_id"]}')
            print(f'参与星数: {len(result["participating_stars"])}')
            print(f'参与的星: {result["participating_stars"]}')
            
            coord_result = result['coordination_result']
            if coord_result.get('success'):
                print(f'执行任务数: {coord_result["tasks_executed"]}')
                print(f'收集结果数: {coord_result["results_collected"]}')
                print(f'总耗时: {coord_result["total_duration"]}')
                
                # 显示各阶段结果
                print('\n📊 各阶段执行结果:')
                coord_results = coord_result['coordination_results']
                for phase, phase_result in coord_results.items():
                    status = '✅' if phase_result.get('success') else '❌'
                    print(f'  {status} {phase}: {phase_result.get("success", False)}')
            else:
                print(f'协调执行失败: {coord_result.get("error")}')
        else:
            print(f'❌ 学习协调失败: {result.get("error")}')
        
        # 测试回测协调
        print('\n🔄 启动回测协调流程...')
        backtest_config = {
            'strategy_name': '测试策略',
            'start_date': '2024-01-01',
            'end_date': '2024-12-31',
            'initial_capital': 1000000
        }
        
        backtest_result = await seven_star_coordinator.start_backtest_coordination(backtest_config)
        
        if backtest_result.get('success'):
            print('✅ 回测协调成功！')
            print(f'回测会话ID: {backtest_result["session_id"]}')
        else:
            print(f'❌ 回测协调失败: {backtest_result.get("error")}')
            
    except Exception as e:
        print(f'❌ 测试执行失败: {e}')
        import traceback
        traceback.print_exc()

if __name__ == "__main__":
    asyncio.run(test_coordinator())
