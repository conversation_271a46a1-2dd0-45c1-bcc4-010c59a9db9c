#!/usr/bin/env python3
"""
反馈分发引擎测试脚本
"""

import sys
import asyncio
sys.path.append('.')

async def test_feedback_engine():
    """测试反馈分发引擎"""
    try:
        from roles.yaoguang_star.core.feedback_distribution_engine import feedback_distribution_engine
        
        print('📤 测试反馈分发引擎...')
        print(f'引擎名称: {feedback_distribution_engine.engine_name}')
        print(f'版本: {feedback_distribution_engine.version}')
        
        # 显示星际反馈偏好
        print('\n🌟 星际反馈偏好配置:')
        for star, preferences in feedback_distribution_engine.star_feedback_preferences.items():
            print(f'  {star}:')
            print(f'    偏好类型: {preferences["preferred_feedback_types"][:2]}...')
            print(f'    反馈频率: {preferences["feedback_frequency"]}')
            print(f'    分发方式: {preferences["delivery_method"]}')
            print(f'    关注领域: {len(preferences["focus_areas"])} 个')
        
        # 测试学习反馈分发
        print('\n🎓 测试学习反馈分发...')
        learning_session_data = {
            "session_id": "learning_session_20250715_001",
            "session_type": "learning",
            "learning_results": {
                "preparation": {"success": True},
                "analysis": {"success": True, "stars_completed": 4},
                "strategy_learning": {"success": True},
                "integration": {"success": True}
            },
            "performance_summary": {
                "overall_progress": 1.0,
                "objectives_achieved": 3,
                "knowledge_gained": 25
            }
        }
        
        learning_result = await feedback_distribution_engine.distribute_learning_feedback(learning_session_data)
        
        if learning_result.get('success'):
            print('✅ 学习反馈分发成功！')
            print(f'会话ID: {learning_result["session_id"]}')
            print(f'反馈项数: {learning_result["feedback_items_generated"]}')
            print(f'目标星数: {learning_result["stars_targeted"]}')
            
            distribution_result = learning_result['distribution_result']
            if distribution_result.get('success'):
                print(f'成功分发: {distribution_result["successful_distributions"]}/{distribution_result["total_distributions"]}')
                print(f'成功率: {distribution_result["success_rate"]:.1%}')
                
                # 显示各星分发结果
                print('\n📊 各星分发结果:')
                for star, result in distribution_result['distribution_results'].items():
                    status = '✅' if result.get('success') else '❌'
                    method = result.get('delivery_method', 'unknown')
                    items = result.get('items_delivered', result.get('items_scheduled', 0))
                    print(f'  {status} {star}: {method} 方式，{items} 项反馈')
            else:
                print(f'分发执行失败: {distribution_result.get("error")}')
        else:
            print(f'❌ 学习反馈分发失败: {learning_result.get("error")}')
        
        # 测试回测反馈分发
        print('\n🔄 测试回测反馈分发...')
        backtest_session_data = {
            "session_id": "backtest_session_20250715_001",
            "session_type": "backtest",
            "backtest_results": {
                "environment_setup": {"success": True},
                "historical_analysis": {"success": True, "stars_completed": 4},
                "strategy_simulation": {"success": True},
                "result_analysis": {"success": True}
            },
            "comprehensive_report": {
                "executive_summary": {
                    "strategy_name": "多因子量化策略",
                    "overall_success": True,
                    "key_metrics": {
                        "total_return": 0.125,
                        "sharpe_ratio": 1.35,
                        "max_drawdown": 0.085
                    }
                }
            }
        }
        
        backtest_result = await feedback_distribution_engine.distribute_backtest_feedback(backtest_session_data)
        
        if backtest_result.get('success'):
            print('✅ 回测反馈分发成功！')
            print(f'会话ID: {backtest_result["session_id"]}')
            print(f'反馈项数: {backtest_result["feedback_items_generated"]}')
            print(f'目标星数: {backtest_result["stars_targeted"]}')
            
            distribution_result = backtest_result['distribution_result']
            if distribution_result.get('success'):
                print(f'成功分发: {distribution_result["successful_distributions"]}/{distribution_result["total_distributions"]}')
                print(f'成功率: {distribution_result["success_rate"]:.1%}')
        else:
            print(f'❌ 回测反馈分发失败: {backtest_result.get("error")}')
        
        # 测试反馈状态查询
        if learning_result.get('success'):
            print('\n📋 测试反馈状态查询...')
            session_id = learning_result["session_id"]
            status_result = await feedback_distribution_engine.get_feedback_status(session_id)
            
            print(f'会话状态: {status_result.get("status")}')
            print(f'会话类型: {status_result.get("session_type")}')
            print(f'反馈项数: {status_result.get("feedback_items", 0)}')
            
            distribution_status = status_result.get('distribution_status', {})
            completed_stars = len([s for s in distribution_status.values() if s == 'completed'])
            total_stars = len(distribution_status)
            print(f'分发完成: {completed_stars}/{total_stars} 星')
        
        # 显示引擎状态
        print(f'\n📈 引擎状态:')
        print(f'活跃反馈会话: {len(feedback_distribution_engine.active_feedback_sessions)}')
        print(f'已完成会话: {len(feedback_distribution_engine.completed_feedback_sessions)}')
        
        # 测试反馈偏好查询
        print('\n🔍 测试反馈偏好查询:')
        for star_name in ['kaiyang', 'tianquan', 'yuheng']:
            preferences = feedback_distribution_engine.star_feedback_preferences.get(star_name, {})
            focus_areas = preferences.get('focus_areas', [])
            print(f'  {star_name}: {len(focus_areas)} 个关注领域 - {focus_areas[0] if focus_areas else "无"}')
            
    except Exception as e:
        print(f'❌ 测试执行失败: {e}')
        import traceback
        traceback.print_exc()

if __name__ == "__main__":
    asyncio.run(test_feedback_engine())
