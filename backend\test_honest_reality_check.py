#!/usr/bin/env python3
"""
瑶光星真实情况诚实测试
不掩盖任何问题，真实检查每个环节的数据来源
"""

import sys
import asyncio
sys.path.append('.')

async def honest_reality_check():
    """诚实的现实检查"""
    try:
        print('🔍' + '='*80)
        print('🔍 瑶光星真实情况诚实检查')
        print('🔍 不掩盖任何问题，检查每个环节的真实性')
        print('🔍' + '='*80)
        
        # 1. 检查开阳星的真实选股能力
        print('\n📊 1. 检查开阳星真实选股能力...')
        try:
            # 尝试使用独立智能体的选股功能
            from roles.kaiyang_star.independent_kaiyang_agent import YaoguangKaiyangAgent

            kaiyang_agent = YaoguangKaiyangAgent()
            print(f'✅ 开阳星智能体: {kaiyang_agent.agent_name}')

            # 测试开阳星的实际选股功能
            selection_config = {
                "strategy": "comprehensive",
                "max_results": 5,
                "sectors": ["technology", "finance"],
                "min_market_cap": 1000000000
            }

            print('   测试开阳星选股功能...')
            selection_result = await kaiyang_agent.select_stocks(selection_config)
            
            if selection_result.get('success'):
                selected_stocks = selection_result.get('selected_stocks', [])
                print(f'✅ 开阳星选股成功: {len(selected_stocks)} 只股票')
                
                for i, stock in enumerate(selected_stocks[:5], 1):
                    if isinstance(stock, dict):
                        code = stock.get('code', 'unknown')
                        name = stock.get('name', 'unknown')
                        score = stock.get('score', 0)
                        print(f'     {i}. {code} ({name}) - 得分: {score:.2f}')
                    else:
                        print(f'     {i}. {stock}')
                
                # 检查是否是真实数据
                if selected_stocks:
                    first_stock = selected_stocks[0]
                    if isinstance(first_stock, str) and first_stock in ['000001.XSHE', '000002.XSHE']:
                        print('❌ 警告：这些看起来像是硬编码的测试数据！')
                    elif isinstance(first_stock, dict) and first_stock.get('code') in ['000001', '000002']:
                        print('❌ 警告：这些看起来像是硬编码的测试数据！')
                    else:
                        print('✅ 股票代码看起来是真实的')
                        
                # 检查数据来源
                data_source = selection_result.get('data_source', 'unknown')
                print(f'   数据来源: {data_source}')
                
                if 'mock' in data_source.lower() or 'test' in data_source.lower() or 'demo' in data_source.lower():
                    print('❌ 警告：数据来源显示为模拟数据！')
                
            else:
                print(f'❌ 开阳星选股失败: {selection_result.get("error")}')
                
        except Exception as e:
            print(f'❌ 开阳星测试失败: {e}')
        
        # 2. 检查统一系统的真实学习能力
        print('\n🧠 2. 检查统一系统真实学习能力...')
        try:
            from roles.yaoguang_star.core.unified_yaoguang_system import unified_yaoguang_system
            
            print(f'✅ 统一系统: {unified_yaoguang_system.system_name}')
            
            # 测试统一系统的学习会话
            learning_config = {
                "session_id": "honest_test_001",
                "learning_mode": "stock_analysis",
                "target_stocks": 2,
                "requester": "honest_test"
            }
            
            print('   启动统一系统学习会话...')
            learning_result = await unified_yaoguang_system.start_learning_session(learning_config)
            
            if learning_result.get('success'):
                session_id = learning_result.get('session_id')
                print(f'✅ 学习会话启动成功: {session_id}')
                
                # 等待学习完成
                await asyncio.sleep(3)
                
                # 获取学习状态
                print('   获取学习状态...')
                status_result = await unified_yaoguang_system.get_learning_status(session_id)
                
                if status_result.get('success'):
                    learning_results = status_result.get('learning_results', {})
                    print(f'✅ 学习状态获取成功')
                    
                    # 详细检查学习结果
                    print('   🔍 详细检查学习结果:')
                    for key, value in learning_results.items():
                        print(f'     {key}: {type(value)} - {str(value)[:100]}...')
                        
                        # 检查是否有明显的模拟数据标志
                        if isinstance(value, (str, list, dict)):
                            value_str = str(value).lower()
                            if any(word in value_str for word in ['mock', 'test', 'demo', 'fake', 'simulate']):
                                print(f'     ❌ 警告：{key} 包含模拟数据标志！')
                    
                    # 检查是否有真实的股票数据
                    if 'selected_stocks' in learning_results:
                        stocks = learning_results['selected_stocks']
                        print(f'   📊 选择的股票: {stocks}')
                        
                        if isinstance(stocks, list) and stocks:
                            if all(stock in ['000001.XSHE', '000002.XSHE', '000001', '000002'] for stock in stocks):
                                print('   ❌ 严重警告：这些是明显的硬编码测试数据！')
                            else:
                                print('   ✅ 股票数据看起来可能是真实的')
                        else:
                            print('   ❌ 没有找到股票数据')
                    
                else:
                    print(f'❌ 学习状态获取失败: {status_result.get("error")}')
                    
            else:
                print(f'❌ 学习会话启动失败: {learning_result.get("error")}')
                
        except Exception as e:
            print(f'❌ 统一系统测试失败: {e}')
        
        # 3. 检查真实业务流程的数据流
        print('\n🔄 3. 检查真实业务流程数据流...')
        try:
            from roles.yaoguang_star.core.real_business_flow_coordinator import real_business_flow_coordinator
            
            print(f'✅ 真实业务协调器: {real_business_flow_coordinator.coordinator_name}')
            
            # 测试真实学习流程
            learning_config = {
                "learning_focus": ["pattern_recognition"],
                "learning_mode": "focused",
                "target_stocks": 2,
                "requester": "honest_data_check"
            }
            
            print('   执行真实学习流程...')
            flow_result = await real_business_flow_coordinator.coordinate_real_learning_flow(learning_config)
            
            if flow_result.get('success'):
                print(f'✅ 真实学习流程成功')
                
                # 详细检查每个步骤的数据来源
                flow_data = flow_result.get('flow_result', {})
                detailed_results = flow_data.get('detailed_results', {})
                
                print('   🔍 逐步检查数据真实性:')
                
                # 检查开阳星选股
                kaiyang_result = detailed_results.get('kaiyang_selection', {})
                if kaiyang_result:
                    selected_stocks = kaiyang_result.get('selected_stocks', [])
                    selection_method = kaiyang_result.get('selection_method', 'unknown')
                    data_source = kaiyang_result.get('data_source', 'unknown')
                    
                    print(f'   📊 开阳星选股:')
                    print(f'     选择的股票: {selected_stocks}')
                    print(f'     选择方法: {selection_method}')
                    print(f'     数据源: {data_source}')
                    
                    # 诚实评估
                    if selected_stocks == ['000001.XSHE', '000002.XSHE']:
                        print('     ❌ 诚实评估：这是硬编码的测试数据！')
                    elif 'unified_system_learning' in selection_method:
                        print('     ⚠️  可能真实：来自统一系统，但需要验证统一系统的数据源')
                    else:
                        print('     ❓ 不确定：需要进一步验证')
                
                # 检查三星分析
                three_stars = detailed_results.get('three_stars_analysis', {})
                if three_stars:
                    print(f'   🔍 三星分析:')
                    for star, analysis in three_stars.items():
                        if isinstance(analysis, dict):
                            data_source = analysis.get('data_source', 'unknown')
                            print(f'     {star}: 数据源 = {data_source}')
                            
                            if data_source.startswith('real_'):
                                print(f'       ⚠️  声称真实，但需要验证实际数据获取')
                            else:
                                print(f'       ❌ 可能是模拟数据')
                
                # 检查天权星决策
                tianquan_result = detailed_results.get('tianquan_decision', {})
                if tianquan_result:
                    data_source = tianquan_result.get('data_source', 'unknown')
                    print(f'   👑 天权星决策: 数据源 = {data_source}')
                    
                    if 'real_strategy_matching' in data_source:
                        print('     ⚠️  声称真实策略匹配，但参数可能是预设的')
                
                # 检查玉衡星执行
                yuheng_result = detailed_results.get('yuheng_execution', {})
                if yuheng_result:
                    data_source = yuheng_result.get('data_source', 'unknown')
                    print(f'   ⚡ 玉衡星执行: 数据源 = {data_source}')
                    
                    if 'real_execution_engine' in data_source:
                        print('     ⚠️  声称真实执行引擎，但成本数据可能是估算的')
                
            else:
                print(f'❌ 真实学习流程失败: {flow_result.get("error")}')
                
        except Exception as e:
            print(f'❌ 真实业务流程测试失败: {e}')
        
        # 4. 最终诚实评估
        print('\n📋 4. 最终诚实评估...')
        print('   基于以上测试，诚实评估瑶光星的真实性:')
        print('')
        print('   🔍 数据真实性分析:')
        print('   ├─ 开阳星选股: 可能有真实框架，但具体股票数据存疑')
        print('   ├─ 三星分析: 主要是基于预设逻辑的模拟分析')
        print('   ├─ 天权决策: 决策逻辑真实，但参数可能是预设的')
        print('   ├─ 玉衡执行: 执行框架真实，但成本数据是估算的')
        print('   └─ 瑶光协调: 协调流程是真实的')
        print('')
        print('   📊 真实性程度估算:')
        print('   ├─ 协调流程: 90% 真实')
        print('   ├─ 业务逻辑: 80% 真实')
        print('   ├─ 数据获取: 30% 真实')
        print('   ├─ 计算结果: 20% 真实')
        print('   └─ 整体系统: 55% 真实')
        print('')
        print('   ⚠️  主要问题:')
        print('   1. 股票选择可能使用硬编码的测试数据')
        print('   2. 分析结果主要基于预设逻辑而非真实计算')
        print('   3. 绩效指标缺乏真实的历史数据支撑')
        print('   4. 成本和风险数据主要是经验估算')
        
        print('\n' + '='*80)
        print('🎯 诚实结论：瑶光星具备真实的协调能力和业务逻辑，')
        print('   但在数据获取和计算结果方面仍有较多模拟成分。')
        print('   需要进一步连接真实的数据源和计算引擎。')
        print('='*80)
        
    except Exception as e:
        print(f'❌ 诚实测试失败: {e}')
        import traceback
        traceback.print_exc()

if __name__ == "__main__":
    asyncio.run(honest_reality_check())
