#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
瑶光星统一时间控制服务
合并时间控制引擎和时间控制服务功能
专注于回测时间控制和防止未来函数
"""

import asyncio
import logging
from datetime import datetime, timedelta
from typing import Dict, List, Any, Optional
from dataclasses import dataclass
from enum import Enum

logger = logging.getLogger(__name__)

class TimeMode(Enum):
    """时间模式"""
    REALTIME = "realtime"      # 实时模式
    BACKTEST = "backtest"      # 回测模式
    SIMULATION = "simulation"  # 模拟模式

@dataclass
class TimeSession:
    """时间会话"""
    session_id: str
    mode: TimeMode
    start_time: datetime
    end_time: Optional[datetime]
    current_time: datetime
    time_delays: Dict[str, timedelta]
    strict_mode: bool

class UnifiedTimeControlService:
    """瑶光星统一时间控制服务"""
    
    def __init__(self):
        self.service_name = "瑶光星统一时间控制服务"
        self.version = "1.0.0"
        
        # 时间会话管理
        self.active_sessions = {}
        self.session_counter = 0
        
        # 默认时间延迟配置
        self.default_time_delays = {
            "news_delay": timedelta(minutes=15),      # 新闻传播延迟15分钟
            "decision_lag": timedelta(minutes=1),     # 决策延迟1分钟
            "execution_lag": timedelta(minutes=5),    # 执行延迟5分钟
            "announcement_delay": timedelta(hours=1), # 公告传播延迟1小时
            "data_processing_delay": timedelta(minutes=2)  # 数据处理延迟2分钟
        }
        
        # 时间控制配置
        self.time_control_config = {
            "strict_mode": True,           # 严格模式防止未来函数
            "max_concurrent_sessions": 10, # 最大并发会话数
            "default_mode": TimeMode.BACKTEST,
            "time_precision": "minute"     # 时间精度：second, minute, hour
        }
        
        logger.info(f"✅ {self.service_name} v{self.version} 初始化完成")
    
    async def create_time_session(self, 
                                 session_type: str,
                                 start_time: datetime,
                                 end_time: Optional[datetime] = None,
                                 mode: TimeMode = TimeMode.BACKTEST,
                                 custom_delays: Dict[str, timedelta] = None) -> Dict[str, Any]:
        """创建时间控制会话"""
        try:
            if len(self.active_sessions) >= self.time_control_config["max_concurrent_sessions"]:
                return {
                    "success": False,
                    "error": "已达到最大并发时间会话数量"
                }
            
            session_id = f"time_session_{datetime.now().strftime('%Y%m%d_%H%M%S')}_{self.session_counter}"
            self.session_counter += 1
            
            # 合并时间延迟配置
            time_delays = self.default_time_delays.copy()
            if custom_delays:
                time_delays.update(custom_delays)
            
            session = TimeSession(
                session_id=session_id,
                mode=mode,
                start_time=start_time,
                end_time=end_time,
                current_time=start_time,
                time_delays=time_delays,
                strict_mode=self.time_control_config["strict_mode"]
            )
            
            self.active_sessions[session_id] = session
            
            logger.info(f"⏰ 创建时间会话: {session_id}, 模式: {mode.value}")
            
            return {
                "success": True,
                "session_id": session_id,
                "mode": mode.value,
                "start_time": start_time.isoformat(),
                "end_time": end_time.isoformat() if end_time else None,
                "time_delays": {k: v.total_seconds() for k, v in time_delays.items()}
            }
            
        except Exception as e:
            logger.error(f"创建时间会话失败: {e}")
            return {
                "success": False,
                "error": str(e)
            }
    
    async def advance_time(self, session_id: str, time_delta: timedelta) -> Dict[str, Any]:
        """推进时间"""
        try:
            if session_id not in self.active_sessions:
                return {
                    "success": False,
                    "error": f"时间会话 {session_id} 不存在"
                }
            
            session = self.active_sessions[session_id]
            
            # 检查时间边界
            new_time = session.current_time + time_delta
            
            if session.end_time and new_time > session.end_time:
                return {
                    "success": False,
                    "error": "时间推进超出会话结束时间"
                }
            
            # 严格模式检查
            if session.strict_mode and session.mode == TimeMode.BACKTEST:
                if new_time > datetime.now():
                    return {
                        "success": False,
                        "error": "严格模式下不允许推进到未来时间"
                    }
            
            # 更新时间
            old_time = session.current_time
            session.current_time = new_time
            
            logger.info(f"⏰ 时间推进: {session_id}, {old_time} -> {new_time}")
            
            return {
                "success": True,
                "session_id": session_id,
                "old_time": old_time.isoformat(),
                "new_time": new_time.isoformat(),
                "time_delta": time_delta.total_seconds()
            }
            
        except Exception as e:
            logger.error(f"时间推进失败: {e}")
            return {
                "success": False,
                "error": str(e)
            }
    
    async def get_current_time(self, session_id: str) -> Dict[str, Any]:
        """获取当前时间"""
        try:
            if session_id not in self.active_sessions:
                return {
                    "success": False,
                    "error": f"时间会话 {session_id} 不存在"
                }
            
            session = self.active_sessions[session_id]
            
            return {
                "success": True,
                "session_id": session_id,
                "current_time": session.current_time.isoformat(),
                "mode": session.mode.value,
                "strict_mode": session.strict_mode
            }
            
        except Exception as e:
            logger.error(f"获取当前时间失败: {e}")
            return {
                "success": False,
                "error": str(e)
            }
    
    async def apply_time_delay(self, session_id: str, delay_type: str) -> Dict[str, Any]:
        """应用时间延迟"""
        try:
            if session_id not in self.active_sessions:
                return {
                    "success": False,
                    "error": f"时间会话 {session_id} 不存在"
                }
            
            session = self.active_sessions[session_id]
            
            if delay_type not in session.time_delays:
                return {
                    "success": False,
                    "error": f"未知的延迟类型: {delay_type}"
                }
            
            delay = session.time_delays[delay_type]
            
            # 应用延迟
            result = await self.advance_time(session_id, delay)
            
            if result["success"]:
                result["delay_type"] = delay_type
                result["delay_seconds"] = delay.total_seconds()
            
            return result
            
        except Exception as e:
            logger.error(f"应用时间延迟失败: {e}")
            return {
                "success": False,
                "error": str(e)
            }
    
    async def validate_time_access(self, session_id: str, requested_time: datetime) -> Dict[str, Any]:
        """验证时间访问（防止未来函数）"""
        try:
            if session_id not in self.active_sessions:
                return {
                    "success": False,
                    "error": f"时间会话 {session_id} 不存在"
                }
            
            session = self.active_sessions[session_id]
            
            # 检查是否访问未来数据
            if session.strict_mode and requested_time > session.current_time:
                return {
                    "success": False,
                    "error": f"未来函数检测：请求时间 {requested_time} 超过当前时间 {session.current_time}",
                    "violation_type": "future_function",
                    "requested_time": requested_time.isoformat(),
                    "current_time": session.current_time.isoformat()
                }
            
            return {
                "success": True,
                "session_id": session_id,
                "requested_time": requested_time.isoformat(),
                "current_time": session.current_time.isoformat(),
                "access_allowed": True
            }
            
        except Exception as e:
            logger.error(f"时间访问验证失败: {e}")
            return {
                "success": False,
                "error": str(e)
            }
    
    async def close_time_session(self, session_id: str) -> Dict[str, Any]:
        """关闭时间会话"""
        try:
            if session_id not in self.active_sessions:
                return {
                    "success": False,
                    "error": f"时间会话 {session_id} 不存在"
                }
            
            session = self.active_sessions[session_id]
            
            # 计算会话统计
            session_duration = session.current_time - session.start_time
            
            # 移除会话
            del self.active_sessions[session_id]
            
            logger.info(f"⏰ 关闭时间会话: {session_id}, 持续时间: {session_duration}")
            
            return {
                "success": True,
                "session_id": session_id,
                "session_duration": session_duration.total_seconds(),
                "start_time": session.start_time.isoformat(),
                "end_time": session.current_time.isoformat()
            }
            
        except Exception as e:
            logger.error(f"关闭时间会话失败: {e}")
            return {
                "success": False,
                "error": str(e)
            }
    
    async def get_time_control_status(self) -> Dict[str, Any]:
        """获取时间控制状态"""
        active_sessions_info = []
        
        for session_id, session in self.active_sessions.items():
            session_info = {
                "session_id": session_id,
                "mode": session.mode.value,
                "current_time": session.current_time.isoformat(),
                "start_time": session.start_time.isoformat(),
                "end_time": session.end_time.isoformat() if session.end_time else None,
                "strict_mode": session.strict_mode
            }
            active_sessions_info.append(session_info)
        
        return {
            "service_name": self.service_name,
            "version": self.version,
            "active_sessions": len(self.active_sessions),
            "max_concurrent_sessions": self.time_control_config["max_concurrent_sessions"],
            "default_time_delays": {k: v.total_seconds() for k, v in self.default_time_delays.items()},
            "time_control_config": self.time_control_config,
            "sessions": active_sessions_info,
            "last_update": datetime.now().isoformat()
        }
    
    async def configure_time_delays(self, session_id: str, new_delays: Dict[str, timedelta]) -> Dict[str, Any]:
        """配置时间延迟"""
        try:
            if session_id not in self.active_sessions:
                return {
                    "success": False,
                    "error": f"时间会话 {session_id} 不存在"
                }
            
            session = self.active_sessions[session_id]
            old_delays = session.time_delays.copy()
            
            # 更新延迟配置
            session.time_delays.update(new_delays)
            
            logger.info(f"⏰ 更新时间延迟配置: {session_id}")
            
            return {
                "success": True,
                "session_id": session_id,
                "old_delays": {k: v.total_seconds() for k, v in old_delays.items()},
                "new_delays": {k: v.total_seconds() for k, v in session.time_delays.items()}
            }
            
        except Exception as e:
            logger.error(f"配置时间延迟失败: {e}")
            return {
                "success": False,
                "error": str(e)
            }

# 全局统一时间控制服务实例
unified_time_control_service = UnifiedTimeControlService()
