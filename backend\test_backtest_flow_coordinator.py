#!/usr/bin/env python3
"""
回测流程协调器测试脚本
"""

import sys
import asyncio
sys.path.append('.')

async def test_backtest_coordinator():
    """测试回测流程协调器"""
    try:
        from roles.yaoguang_star.core.backtest_flow_coordinator import backtest_flow_coordinator, BacktestConfig
        
        print('🔄 测试回测流程协调器...')
        print(f'协调器名称: {backtest_flow_coordinator.coordinator_name}')
        print(f'版本: {backtest_flow_coordinator.version}')
        
        # 显示星际回测能力
        print('\n🌟 星际回测能力映射:')
        for star, capabilities in backtest_flow_coordinator.star_backtest_capabilities.items():
            print(f'  {star}: {len(capabilities["backtest_functions"])} 个回测功能')
            print(f'    功能: {capabilities["backtest_functions"][:2]}...')
            print(f'    时序分析: {capabilities["time_series_analysis"]}')
        
        # 创建回测配置
        backtest_config = BacktestConfig(
            backtest_id="test_backtest_001",
            strategy_name="多因子量化策略",
            start_date="2024-01-01",
            end_date="2024-12-31",
            initial_capital=1000000.0,
            benchmark="000300.XSHG",
            universe_size=100,
            rebalance_frequency="monthly",
            commission_rate=0.0003,
            slippage_rate=0.001,
            max_position_size=0.1
        )
        
        print('\n🚀 启动综合回测流程...')
        print(f'策略名称: {backtest_config.strategy_name}')
        print(f'回测期间: {backtest_config.start_date} 至 {backtest_config.end_date}')
        print(f'初始资金: {backtest_config.initial_capital:,.0f} 元')
        print(f'股票池大小: {backtest_config.universe_size}')
        print(f'重平衡频率: {backtest_config.rebalance_frequency}')
        
        result = await backtest_flow_coordinator.start_comprehensive_backtest(backtest_config)
        
        if result.get('success'):
            print('✅ 综合回测成功！')
            print(f'会话ID: {result["session_id"]}')
            print(f'回测期间: {result["backtest_period"]}')
            
            backtest_result = result['backtest_result']
            if backtest_result.get('success'):
                print(f'总耗时: {backtest_result["total_duration"]}')
                print(f'时间线点数: {backtest_result["timeline_points"]}')
                
                # 显示各阶段结果
                print('\n📊 各回测阶段结果:')
                backtest_results = backtest_result['backtest_results']
                for stage, stage_result in backtest_results.items():
                    status = '✅' if stage_result.get('success') else '❌'
                    print(f'  {status} {stage}: {stage_result.get("success", False)}')
                    
                    # 显示详细信息
                    if stage == 'environment_setup' and stage_result.get('success'):
                        print(f'    时间线点数: {stage_result.get("timeline_points", 0)}')
                        data_prep = stage_result.get('data_preparation', {})
                        prepared_count = sum(1 for v in data_prep.values() if v)
                        print(f'    数据准备: {prepared_count}/{len(data_prep)} 项完成')
                    
                    elif stage == 'historical_analysis' and stage_result.get('success'):
                        print(f'    完成星数: {stage_result.get("stars_completed", 0)}')
                        print(f'历史决策数: {stage_result.get("total_historical_decisions", 0)}')
                        print(f'时间线覆盖: {stage_result.get("timeline_coverage", 0)}')
                    
                    elif stage == 'strategy_simulation' and stage_result.get('success'):
                        print(f'    组合决策: {stage_result.get("portfolio_decisions", 0)}')
                        print(f'执行交易: {stage_result.get("execution_transactions", 0)}')
                
                # 显示综合报告
                print('\n📈 综合回测报告:')
                comprehensive_report = backtest_result.get('comprehensive_report', {})
                executive_summary = comprehensive_report.get('executive_summary', {})
                
                if executive_summary:
                    print(f'  策略名称: {executive_summary.get("strategy_name", "未知")}')
                    print(f'  整体成功: {executive_summary.get("overall_success", False)}')
                    
                    key_metrics = executive_summary.get('key_metrics', {})
                    if key_metrics:
                        print(f'  总收益率: {key_metrics.get("total_return", 0):.1%}')
                        print(f'  夏普比率: {key_metrics.get("sharpe_ratio", 0):.2f}')
                        print(f'  最大回撤: {key_metrics.get("max_drawdown", 0):.1%}')
                
                recommendations = comprehensive_report.get('recommendations', [])
                if recommendations:
                    print(f'\n💡 改进建议 ({len(recommendations)} 项):')
                    for i, rec in enumerate(recommendations[:3], 1):
                        print(f'  {i}. {rec}')
                
                # 显示性能摘要
                print('\n📈 回测性能摘要:')
                perf_summary = backtest_result.get('performance_summary', {})
                print(f'  整体进度: {perf_summary.get("overall_progress", 0):.1%}')
                
                stage_completion = perf_summary.get('stage_completion', {})
                completed_stages = len([s for s in stage_completion.values() if s])
                total_stages = len(stage_completion)
                print(f'  阶段完成率: {completed_stages}/{total_stages} ({completed_stages/max(1,total_stages):.1%})')
                
            else:
                print(f'回测执行失败: {backtest_result.get("error")}')
        else:
            print(f'❌ 综合回测失败: {result.get("error")}')
        
        # 显示活跃会话状态
        print(f'\n📋 活跃回测会话数: {len(backtest_flow_coordinator.active_backtest_sessions)}')
        print(f'已完成回测会话数: {len(backtest_flow_coordinator.completed_backtest_sessions)}')
        
        # 测试回测能力查询
        print('\n🔍 测试回测能力查询:')
        for star_name in ['kaiyang', 'tianquan', 'yuheng']:
            capabilities = backtest_flow_coordinator.star_backtest_capabilities.get(star_name, {})
            functions = capabilities.get('backtest_functions', [])
            print(f'  {star_name}: {len(functions)} 个功能 - {functions[0] if functions else "无"}')
            
    except Exception as e:
        print(f'❌ 测试执行失败: {e}')
        import traceback
        traceback.print_exc()

if __name__ == "__main__":
    asyncio.run(test_backtest_coordinator())
