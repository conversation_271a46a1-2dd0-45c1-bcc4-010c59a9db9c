"""
综合回测引擎
实现完整的回测流程：历史数据准备 → 策略定义 → 信号生成 → 交易模拟 → 风险控制 → 绩效计算 → 结果分析 → 报告生成
"""

import asyncio
import logging
import pandas as pd
import numpy as np
from datetime import datetime, timed<PERSON>ta
from typing import Dict, List, Any, Optional, Tuple
from enum import Enum
from dataclasses import dataclass

logger = logging.getLogger(__name__)

class BacktestPhase(Enum):
    """回测阶段"""
    DATA_PREPARATION = "data_preparation"        # 历史数据准备
    STRATEGY_DEFINITION = "strategy_definition"  # 策略定义
    SIGNAL_GENERATION = "signal_generation"      # 信号生成
    TRADE_SIMULATION = "trade_simulation"        # 交易模拟
    RISK_CONTROL = "risk_control"               # 风险控制
    PERFORMANCE_CALCULATION = "performance_calculation" # 绩效计算
    RESULT_ANALYSIS = "result_analysis"         # 结果分析
    REPORT_GENERATION = "report_generation"     # 报告生成

@dataclass
class BacktestConfig:
    """回测配置"""
    strategy_name: str
    stock_codes: List[str]
    start_date: str
    end_date: str
    initial_capital: float = 1000000.0
    commission_rate: float = 0.0003
    slippage_rate: float = 0.001
    max_position_size: float = 0.1  # 最大仓位10%
    risk_free_rate: float = 0.03    # 无风险利率3%

@dataclass
class TradeSignal:
    """交易信号"""
    timestamp: datetime
    stock_code: str
    signal_type: str  # 'buy', 'sell', 'hold'
    price: float
    quantity: int
    confidence: float
    reason: str

@dataclass
class Trade:
    """交易记录"""
    trade_id: str
    timestamp: datetime
    stock_code: str
    side: str  # 'buy', 'sell'
    price: float
    quantity: int
    commission: float
    slippage: float
    total_cost: float

@dataclass
class PerformanceMetrics:
    """绩效指标"""
    total_return: float
    annual_return: float
    sharpe_ratio: float
    max_drawdown: float
    volatility: float
    win_rate: float
    profit_factor: float
    calmar_ratio: float
    sortino_ratio: float
    information_ratio: float

class BacktestSession:
    """回测会话"""
    def __init__(self, session_id: str, config: BacktestConfig):
        self.session_id = session_id
        self.config = config
        self.current_phase = BacktestPhase.DATA_PREPARATION
        self.start_time = datetime.now()
        self.end_time = None
        self.phases_completed = []
        
        # 回测数据
        self.historical_data = {}
        self.strategy_rules = {}
        self.signals = []
        self.trades = []
        self.portfolio_value = []
        self.performance_metrics = None
        self.analysis_results = {}
        self.report = {}

class ComprehensiveBacktestEngine:
    """综合回测引擎"""
    
    def __init__(self):
        self.engine_name = "瑶光星综合回测引擎"
        self.version = "1.0.0"
        self.active_sessions: Dict[str, BacktestSession] = {}
        self.completed_sessions: List[BacktestSession] = []
        
        # 回测配置
        self.backtest_config = {
            "max_concurrent_sessions": 5,
            "enable_real_data_only": True,
            "enable_risk_control": True,
            "enable_slippage_simulation": True,
            "enable_commission_calculation": True,
            "benchmark_index": "000300.XSHG"  # 沪深300作为基准
        }
        
        logger.info(f"✅ {self.engine_name} v{self.version} 初始化完成")
    
    async def start_backtest_session(self, config: BacktestConfig) -> Dict[str, Any]:
        """启动回测会话"""
        try:
            session_id = f"backtest_{datetime.now().strftime('%Y%m%d_%H%M%S')}"
            
            if len(self.active_sessions) >= self.backtest_config["max_concurrent_sessions"]:
                return {
                    "success": False,
                    "error": "已达到最大并发回测会话数量",
                    "max_sessions": self.backtest_config["max_concurrent_sessions"]
                }
            
            # 创建回测会话
            session = BacktestSession(session_id, config)
            self.active_sessions[session_id] = session
            
            logger.info(f"🔄 启动回测会话: {session_id}")
            
            # 执行完整的回测流程
            backtest_result = await self._execute_backtest_flow(session)
            
            return {
                "success": True,
                "session_id": session_id,
                "config": config.__dict__,
                "backtest_result": backtest_result,
                "current_phase": session.current_phase.value,
                "start_time": session.start_time.isoformat()
            }
            
        except Exception as e:
            logger.error(f"启动回测会话失败: {e}")
            return {
                "success": False,
                "error": str(e)
            }
    
    async def _execute_backtest_flow(self, session: BacktestSession) -> Dict[str, Any]:
        """执行完整的回测流程"""
        try:
            backtest_results = {}
            
            # 阶段1: 历史数据准备
            if session.current_phase == BacktestPhase.DATA_PREPARATION:
                data_result = await self._data_preparation_phase(session)
                backtest_results["data_preparation"] = data_result
                session.phases_completed.append("data_preparation")
                
                if data_result.get("success"):
                    session.current_phase = BacktestPhase.STRATEGY_DEFINITION
            
            # 阶段2: 策略定义
            if session.current_phase == BacktestPhase.STRATEGY_DEFINITION:
                strategy_result = await self._strategy_definition_phase(session)
                backtest_results["strategy_definition"] = strategy_result
                session.phases_completed.append("strategy_definition")
                
                if strategy_result.get("success"):
                    session.current_phase = BacktestPhase.SIGNAL_GENERATION
            
            # 阶段3: 信号生成
            if session.current_phase == BacktestPhase.SIGNAL_GENERATION:
                signal_result = await self._signal_generation_phase(session)
                backtest_results["signal_generation"] = signal_result
                session.phases_completed.append("signal_generation")
                
                if signal_result.get("success"):
                    session.current_phase = BacktestPhase.TRADE_SIMULATION
            
            # 阶段4: 交易模拟
            if session.current_phase == BacktestPhase.TRADE_SIMULATION:
                trade_result = await self._trade_simulation_phase(session)
                backtest_results["trade_simulation"] = trade_result
                session.phases_completed.append("trade_simulation")
                
                if trade_result.get("success"):
                    session.current_phase = BacktestPhase.RISK_CONTROL
            
            # 阶段5: 风险控制
            if session.current_phase == BacktestPhase.RISK_CONTROL:
                risk_result = await self._risk_control_phase(session)
                backtest_results["risk_control"] = risk_result
                session.phases_completed.append("risk_control")
                
                if risk_result.get("success"):
                    session.current_phase = BacktestPhase.PERFORMANCE_CALCULATION
            
            # 阶段6: 绩效计算
            if session.current_phase == BacktestPhase.PERFORMANCE_CALCULATION:
                performance_result = await self._performance_calculation_phase(session)
                backtest_results["performance_calculation"] = performance_result
                session.phases_completed.append("performance_calculation")
                
                if performance_result.get("success"):
                    session.current_phase = BacktestPhase.RESULT_ANALYSIS
            
            # 阶段7: 结果分析
            if session.current_phase == BacktestPhase.RESULT_ANALYSIS:
                analysis_result = await self._result_analysis_phase(session)
                backtest_results["result_analysis"] = analysis_result
                session.phases_completed.append("result_analysis")
                
                if analysis_result.get("success"):
                    session.current_phase = BacktestPhase.REPORT_GENERATION
            
            # 阶段8: 报告生成
            if session.current_phase == BacktestPhase.REPORT_GENERATION:
                report_result = await self._report_generation_phase(session)
                backtest_results["report_generation"] = report_result
                session.phases_completed.append("report_generation")
            
            # 完成回测会话
            session.end_time = datetime.now()
            self.completed_sessions.append(session)
            if session.session_id in self.active_sessions:
                del self.active_sessions[session.session_id]
            
            return {
                "success": True,
                "phases_completed": session.phases_completed,
                "backtest_results": backtest_results,
                "total_duration": str(session.end_time - session.start_time),
                "performance_summary": session.performance_metrics.__dict__ if session.performance_metrics else {}
            }
            
        except Exception as e:
            logger.error(f"回测流程执行失败: {e}")
            return {
                "success": False,
                "error": str(e)
            }

    async def _data_preparation_phase(self, session: BacktestSession) -> Dict[str, Any]:
        """历史数据准备阶段"""
        try:
            logger.info(f"📊 执行历史数据准备阶段: {session.session_id}")

            from .real_data_manager import real_data_manager

            config = session.config

            # 为每只股票收集历史数据
            for stock_code in config.stock_codes:
                stock_data = await real_data_manager.get_real_stock_data(
                    stock_code, config.start_date, config.end_date
                )

                if not stock_data.empty:
                    session.historical_data[stock_code] = stock_data
                    logger.info(f"✅ 收集到 {stock_code} 的 {len(stock_data)} 条历史数据")
                else:
                    logger.warning(f"⚠️ 未找到 {stock_code} 的历史数据")

            # 收集基准指数数据
            benchmark_data = await real_data_manager.get_real_stock_data(
                self.backtest_config["benchmark_index"],
                config.start_date,
                config.end_date
            )

            if not benchmark_data.empty:
                session.historical_data["benchmark"] = benchmark_data

            # 数据质量检查
            data_quality = self._check_data_quality(session.historical_data)

            return {
                "success": True,
                "phase": "data_preparation",
                "stocks_with_data": len(session.historical_data),
                "data_quality": data_quality,
                "next_phase": "strategy_definition"
            }

        except Exception as e:
            logger.error(f"历史数据准备阶段失败: {e}")
            return {"success": False, "error": str(e)}

    def _check_data_quality(self, historical_data: Dict[str, pd.DataFrame]) -> Dict[str, Any]:
        """检查数据质量"""
        quality_report = {
            "total_stocks": len(historical_data),
            "stocks_with_sufficient_data": 0,
            "average_data_points": 0,
            "data_completeness": 0.0
        }

        if not historical_data:
            return quality_report

        total_points = 0
        sufficient_data_count = 0

        for stock_code, data in historical_data.items():
            if stock_code == "benchmark":
                continue

            data_points = len(data)
            total_points += data_points

            # 认为至少需要30个交易日的数据
            if data_points >= 30:
                sufficient_data_count += 1

        stock_count = len(historical_data) - (1 if "benchmark" in historical_data else 0)

        if stock_count > 0:
            quality_report["stocks_with_sufficient_data"] = sufficient_data_count
            quality_report["average_data_points"] = total_points / stock_count
            quality_report["data_completeness"] = sufficient_data_count / stock_count

        return quality_report

    async def _strategy_definition_phase(self, session: BacktestSession) -> Dict[str, Any]:
        """策略定义阶段"""
        try:
            logger.info(f"📋 执行策略定义阶段: {session.session_id}")

            # 定义默认的技术分析策略
            strategy_rules = {
                "strategy_type": "technical_analysis",
                "indicators": {
                    "ma_short": 5,   # 5日均线
                    "ma_long": 20,   # 20日均线
                    "rsi_period": 14, # RSI周期
                    "rsi_oversold": 30,  # RSI超卖线
                    "rsi_overbought": 70 # RSI超买线
                },
                "entry_rules": [
                    "ma_short > ma_long",  # 短期均线上穿长期均线
                    "rsi < rsi_oversold"   # RSI超卖
                ],
                "exit_rules": [
                    "ma_short < ma_long",  # 短期均线下穿长期均线
                    "rsi > rsi_overbought" # RSI超买
                ],
                "position_sizing": {
                    "method": "equal_weight",
                    "max_position": session.config.max_position_size
                }
            }

            session.strategy_rules = strategy_rules

            return {
                "success": True,
                "phase": "strategy_definition",
                "strategy_type": strategy_rules["strategy_type"],
                "indicators_count": len(strategy_rules["indicators"]),
                "entry_rules_count": len(strategy_rules["entry_rules"]),
                "exit_rules_count": len(strategy_rules["exit_rules"]),
                "next_phase": "signal_generation"
            }

        except Exception as e:
            logger.error(f"策略定义阶段失败: {e}")
            return {"success": False, "error": str(e)}

# 全局实例
comprehensive_backtest_engine = ComprehensiveBacktestEngine()
