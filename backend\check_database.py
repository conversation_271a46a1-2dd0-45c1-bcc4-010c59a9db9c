#!/usr/bin/env python3
"""
检查数据库结构和内容
"""

import sqlite3
import os

def check_database(db_path):
    """检查数据库结构和内容"""
    if not os.path.exists(db_path):
        print(f"❌ 数据库不存在: {db_path}")
        return
    
    try:
        conn = sqlite3.connect(db_path)
        cursor = conn.cursor()
        
        print(f"✅ 数据库: {db_path}")
        
        # 获取所有表
        cursor.execute("SELECT name FROM sqlite_master WHERE type='table'")
        tables = cursor.fetchall()
        
        if not tables:
            print("   ❌ 数据库中没有表")
            conn.close()
            return
        
        print(f"   📊 包含 {len(tables)} 个表:")
        
        for table in tables:
            table_name = table[0]
            print(f"     - {table_name}")
            
            # 获取表结构
            cursor.execute(f"PRAGMA table_info({table_name})")
            columns = cursor.fetchall()
            
            if columns:
                print(f"       字段: {[col[1] for col in columns]}")
                
                # 获取数据量
                cursor.execute(f"SELECT COUNT(*) FROM {table_name}")
                count = cursor.fetchone()[0]
                print(f"       数据量: {count} 行")
                
                # 如果有数据，显示前几行
                if count > 0:
                    cursor.execute(f"SELECT * FROM {table_name} LIMIT 3")
                    sample_data = cursor.fetchall()
                    print(f"       样本数据: {sample_data}")
            else:
                print("       ❌ 无法获取表结构")
        
        conn.close()
        
    except Exception as e:
        print(f"❌ 数据库检查失败: {e}")

if __name__ == "__main__":
    # 检查所有可能的数据库
    databases = [
        "data/stock_master.db",
        "data/stock_historical.db", 
        "data/stock_realtime.db",
        "data/stock_database.db",
        "data/realtime_stock_data.db"
    ]
    
    print("🔍 检查数据库结构和内容...")
    print("="*60)
    
    for db_path in databases:
        check_database(db_path)
        print()
