/**
 * 确定性数据提供器
 * 为前端组件提供一致的模拟数据
 */

// 基础确定性值生成器
export function generateDeterministicValue(seed: string, min: number = 0, max: number = 1): number {
  // 确保seed是有效的字符串
  if (!seed || typeof seed !== 'string') {
    seed = 'default_seed'
  }

  let hash = 0
  for (let i = 0; i < seed.length; i++) {
    const char = seed.charCodeAt(i)
    hash = ((hash << 5) - hash) + char
    hash = hash & hash // 转换为32位整数
  }
  
  // 将hash转换为0-1之间的值
  const normalized = Math.abs(hash) / 2147483647
  return min + (normalized * (max - min))
}

// 确定性整数生成器
export function generateDeterministicInt(seed: string, min: number = 0, max: number = 100): number {
  return Math.floor(generateDeterministicValue(seed, min, max + 1))
}

// 确定性范围生成器
export function generateDeterministicRange(seed: string, count: number, min: number = 0, max: number = 100): number[] {
  const result = []
  for (let i = 0; i < count; i++) {
    result.push(generateDeterministicValue(`${seed}_${i}`, min, max))
  }
  return result
}

// 计算现实的分销商数量
export function calculateRealisticDistributorCount(region: string): number {
  const baseCounts = {
    'north': 150,
    'south': 200,
    'east': 180,
    'west': 120,
    'central': 160
  }
  
  const baseCount = baseCounts[region as keyof typeof baseCounts] || 150
  const variation = generateDeterministicValue(region, -20, 20)
  return Math.max(50, Math.floor(baseCount + variation))
}

// 计算现实的佣金率
export function calculateRealisticCommission(productType: string): number {
  const baseRates = {
    'premium': 0.15,
    'standard': 0.12,
    'basic': 0.08,
    'enterprise': 0.20
  }
  
  const baseRate = baseRates[productType as keyof typeof baseRates] || 0.12
  const variation = generateDeterministicValue(productType, -0.02, 0.02)
  return Math.max(0.05, Math.min(0.25, baseRate + variation))
}

// 计算现实的增长率
export function calculateRealisticGrowthRate(timeframe: string): number {
  const baseRates = {
    'monthly': 0.08,
    'quarterly': 0.25,
    'yearly': 1.2,
    'weekly': 0.02
  }
  
  const baseRate = baseRates[timeframe as keyof typeof baseRates] || 0.08
  const variation = generateDeterministicValue(timeframe, -0.03, 0.05)
  return Math.max(-0.1, Math.min(2.0, baseRate + variation))
}

// 计算服务成功率
export function calculateServiceSuccessRate(serviceType: string): number {
  const baseRates = {
    'api': 0.98,
    'database': 0.95,
    'network': 0.92,
    'external': 0.88,
    'ai': 0.94
  }
  
  const baseRate = baseRates[serviceType as keyof typeof baseRates] || 0.95
  const variation = generateDeterministicValue(serviceType, -0.05, 0.02)
  return Math.max(0.7, Math.min(1.0, baseRate + variation))
}

// 计算服务响应时间
export function calculateServiceResponseTime(serviceType: string): number {
  const baseTimes = {
    'api': 120,
    'database': 80,
    'network': 200,
    'external': 500,
    'ai': 300
  }
  
  const baseTime = baseTimes[serviceType as keyof typeof baseTimes] || 150
  const variation = generateDeterministicValue(serviceType, -50, 100)
  return Math.max(10, Math.floor(baseTime + variation))
}

// 生成股票价格数据
export function generateStockPriceData(symbol: string, days: number = 30): Array<{date: string, price: number, volume: number}> {
  const result = []
  const basePrice = generateDeterministicValue(symbol, 10, 200)
  
  for (let i = 0; i < days; i++) {
    const date = new Date()
    date.setDate(date.getDate() - (days - i))
    
    const priceVariation = generateDeterministicValue(`${symbol}_${i}`, -0.05, 0.05)
    const price = Math.max(1, basePrice * (1 + priceVariation))
    
    const volumeVariation = generateDeterministicValue(`${symbol}_vol_${i}`, 0.5, 2.0)
    const volume = Math.floor(1000000 * volumeVariation)
    
    result.push({
      date: date.toISOString().split('T')[0],
      price: Math.round(price * 100) / 100,
      volume
    })
  }
  
  return result
}

// 生成市场指标数据
export function generateMarketIndicators(market: string): {
  rsi: number,
  macd: number,
  bollinger: { upper: number, middle: number, lower: number },
  volume: number
} {
  return {
    rsi: generateDeterministicValue(`${market}_rsi`, 20, 80),
    macd: generateDeterministicValue(`${market}_macd`, -2, 2),
    bollinger: {
      upper: generateDeterministicValue(`${market}_bb_upper`, 100, 120),
      middle: generateDeterministicValue(`${market}_bb_middle`, 90, 110),
      lower: generateDeterministicValue(`${market}_bb_lower`, 80, 100)
    },
    volume: Math.floor(generateDeterministicValue(`${market}_volume`, 1000000, 10000000))
  }
}

// 生成角色性能数据
export function generateRolePerformance(roleId: string): {
  efficiency: number,
  accuracy: number,
  responseTime: number,
  taskCount: number
} {
  return {
    efficiency: generateDeterministicValue(`${roleId}_efficiency`, 0.7, 0.98),
    accuracy: generateDeterministicValue(`${roleId}_accuracy`, 0.8, 0.99),
    responseTime: Math.floor(generateDeterministicValue(`${roleId}_response`, 100, 2000)),
    taskCount: Math.floor(generateDeterministicValue(`${roleId}_tasks`, 50, 500))
  }
}

// 生成系统状态数据
export function generateSystemStatus(): {
  cpu: number,
  memory: number,
  disk: number,
  network: number,
  uptime: number
} {
  const timestamp = Math.floor(Date.now() / 1000000) // 降低变化频率
  
  return {
    cpu: generateDeterministicValue(`cpu_${timestamp}`, 10, 80),
    memory: generateDeterministicValue(`memory_${timestamp}`, 30, 90),
    disk: generateDeterministicValue(`disk_${timestamp}`, 20, 70),
    network: generateDeterministicValue(`network_${timestamp}`, 5, 95),
    uptime: Math.floor(generateDeterministicValue(`uptime_${timestamp}`, 86400, 2592000)) // 1天到30天
  }
}

// 生成新闻数据
export function generateNewsData(count: number = 10): Array<{
  id: string,
  title: string,
  summary: string,
  sentiment: 'positive' | 'negative' | 'neutral',
  timestamp: string,
  source: string
}> {
  const sources = ['财经网', '新浪财经', '东方财富', '证券时报', '第一财经']
  const sentiments: ('positive' | 'negative' | 'neutral')[] = ['positive', 'negative', 'neutral']
  
  const result = []
  for (let i = 0; i < count; i++) {
    const timestamp = new Date()
    timestamp.setHours(timestamp.getHours() - i)
    
    result.push({
      id: `news_${i}`,
      title: `市场动态新闻 ${i + 1}`,
      summary: `这是一条关于市场动态的新闻摘要，包含了重要的市场信息和分析。`,
      sentiment: sentiments[generateDeterministicInt(`sentiment_${i}`, 0, 2)],
      timestamp: timestamp.toISOString(),
      source: sources[generateDeterministicInt(`source_${i}`, 0, sources.length - 1)]
    })
  }
  
  return result
}

export default {
  generateDeterministicValue,
  generateDeterministicInt,
  generateDeterministicRange,
  calculateRealisticDistributorCount,
  calculateRealisticCommission,
  calculateRealisticGrowthRate,
  calculateServiceSuccessRate,
  calculateServiceResponseTime,
  generateStockPriceData,
  generateMarketIndicators,
  generateRolePerformance,
  generateSystemStatus,
  generateNewsData
}
