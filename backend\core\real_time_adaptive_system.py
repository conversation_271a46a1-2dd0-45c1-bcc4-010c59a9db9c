#!/usr/bin/env python3
# -*- coding: utf-8 -*-

"""
实时适应性系统
基于市场变化的动态调整，真正的实时响应系统

功能：
1. 实时市场监控
2. 动态策略调整
3. 自适应参数优化
4. 异常检测和响应
5. 多维度风险控制
"""

import asyncio
import logging
import numpy as np
import pandas as pd
from datetime import datetime, timedelta
from typing import Dict, List, Any, Optional, Callable
from dataclasses import dataclass, field
from enum import Enum
import json
import sqlite3
import aiosqlite
from collections import deque, defaultdict
import threading
import time

logger = logging.getLogger(__name__)

class MarketCondition(str, Enum):
    """市场状态"""
    BULL_MARKET = "bull_market"      # 牛市
    BEAR_MARKET = "bear_market"      # 熊市
    SIDEWAYS = "sideways"            # 震荡市
    HIGH_VOLATILITY = "high_volatility"  # 高波动
    LOW_VOLATILITY = "low_volatility"    # 低波动
    CRISIS = "crisis"                # 危机状态

class AdaptiveStrategy(str, Enum):
    """自适应策略"""
    MOMENTUM = "momentum"            # 动量策略
    MEAN_REVERSION = "mean_reversion"  # 均值回归
    BREAKOUT = "breakout"           # 突破策略
    DEFENSIVE = "defensive"         # 防御策略
    AGGRESSIVE = "aggressive"       # 激进策略
    BALANCED = "balanced"           # 平衡策略

@dataclass
class MarketSignal:
    """市场信号"""
    signal_id: str
    signal_type: str
    strength: float  # 信号强度 0-1
    confidence: float  # 置信度 0-1
    timestamp: datetime
    source: str
    data: Dict[str, Any] = field(default_factory=dict)

    @property
    def is_strong(self) -> bool:
        return self.strength > 0.7 and self.confidence > 0.8

@dataclass
class AdaptiveParameter:
    """自适应参数"""
    name: str
    current_value: float
    min_value: float
    max_value: float
    adaptation_rate: float = 0.1
    last_update: datetime = field(default_factory=datetime.now)
    update_history: List[float] = field(default_factory=list)

    def update(self, new_value: float, force: bool = False):
        """更新参数值"""
        if force or abs(new_value - self.current_value) > self.adaptation_rate:
            self.current_value = max(self.min_value, min(self.max_value, new_value))
            self.update_history.append(self.current_value)
            self.last_update = datetime.now()

            # 保持历史记录大小
            if len(self.update_history) > 1000:
                self.update_history = self.update_history[-1000:]

class RealTimeMarketMonitor:
    """实时市场监控器"""

    def __init__(self, update_interval: float = 1.0):
        self.update_interval = update_interval
        self.is_running = False
        self.monitor_task: Optional[asyncio.Task] = None

        # 市场数据缓存
        self.price_data: Dict[str, deque] = defaultdict(lambda: deque(maxlen=1000))
        self.volume_data: Dict[str, deque] = defaultdict(lambda: deque(maxlen=1000))
        self.market_indicators: Dict[str, float] = {}

        # 信号队列
        self.signal_queue: deque = deque(maxlen=10000)
        self.signal_callbacks: List[Callable] = []

        # 市场状态
        self.current_market_condition = MarketCondition.SIDEWAYS
        self.market_volatility = 0.0
        self.market_trend = 0.0  # -1到1，负数为下跌趋势

    async def start_monitoring(self):
        """开始监控"""
        if self.is_running:
            return

        self.is_running = True
        self.monitor_task = asyncio.create_task(self._monitoring_loop())
        logger.info("📊 实时市场监控已启动")

    async def stop_monitoring(self):
        """停止监控"""
        self.is_running = False
        if self.monitor_task:
            self.monitor_task.cancel()
            try:
                await self.monitor_task
            except asyncio.CancelledError:
                pass
        logger.info("⏹️ 实时市场监控已停止")

    async def _monitoring_loop(self):
        """监控主循环"""
        try:
            while self.is_running:
                # 获取实时数据
                await self._fetch_real_time_data()

                # 计算市场指标
                await self._calculate_market_indicators()

                # 检测市场状态变化
                await self._detect_market_condition_change()

                # 生成信号
                await self._generate_market_signals()

                # 触发回调
                await self._trigger_callbacks()

                await asyncio.sleep(self.update_interval)

        except asyncio.CancelledError:
            logger.info("监控循环被取消")
        except Exception as e:
            logger.error(f"监控循环异常: {e}")

    async def _fetch_real_time_data(self):
        """获取实时数据"""
        try:
            # 这里应该连接真实的数据源
            # 为了演示，我们生成模拟数据
            current_time = datetime.now()

            # 模拟主要指数数据
            indices = ["000001", "399001", "000300"]  # 上证、深证、沪深300

            for index in indices:
                # 生成模拟价格数据（基于随机游走）
                if self.price_data[index]:
                    last_price = self.price_data[index][-1]
                    change = np.random.normal(0, 0.01)  # 1%标准差
                    new_price = last_price * (1 + change)
                else:
                    new_price = 3000.0  # 初始价格

                # 生成模拟成交量
                base_volume = 1000000
                volume_change = np.random.normal(0, 0.2)
                new_volume = base_volume * (1 + volume_change)

                self.price_data[index].append(new_price)
                self.volume_data[index].append(max(0, new_volume))

        except Exception as e:
            logger.error(f"获取实时数据失败: {e}")

    async def _calculate_market_indicators(self):
        """计算市场指标"""
        try:
            for symbol in self.price_data:
                prices = list(self.price_data[symbol])
                volumes = list(self.volume_data[symbol])

                if len(prices) < 20:
                    continue

                # 计算技术指标
                prices_array = np.array(prices)

                # 移动平均
                ma5 = np.mean(prices_array[-5:])
                ma20 = np.mean(prices_array[-20:])

                # 波动率（20日）
                returns = np.diff(prices_array[-21:]) / prices_array[-21:-1]
                volatility = np.std(returns) * np.sqrt(252)  # 年化波动率

                # 趋势强度
                trend = (ma5 - ma20) / ma20

                # RSI
                rsi = self._calculate_rsi(prices_array)

                # 更新指标
                self.market_indicators[f"{symbol}_ma5"] = ma5
                self.market_indicators[f"{symbol}_ma20"] = ma20
                self.market_indicators[f"{symbol}_volatility"] = volatility
                self.market_indicators[f"{symbol}_trend"] = trend
                self.market_indicators[f"{symbol}_rsi"] = rsi

            # 计算整体市场指标
            if self.market_indicators:
                all_volatilities = [v for k, v in self.market_indicators.items() if "volatility" in k]
                all_trends = [v for k, v in self.market_indicators.items() if "trend" in k]

                if all_volatilities:
                    self.market_volatility = np.mean(all_volatilities)
                if all_trends:
                    self.market_trend = np.mean(all_trends)

        except Exception as e:
            logger.error(f"计算市场指标失败: {e}")

    def _calculate_rsi(self, prices: np.ndarray, period: int = 14) -> float:
        """计算RSI指标"""
        try:
            if len(prices) < period + 1:
                return 50.0

            deltas = np.diff(prices)
            gains = np.where(deltas > 0, deltas, 0)
            losses = np.where(deltas < 0, -deltas, 0)

            avg_gain = np.mean(gains[-period:])
            avg_loss = np.mean(losses[-period:])

            if avg_loss == 0:
                return 100.0

            rs = avg_gain / avg_loss
            rsi = 100 - (100 / (1 + rs))

            return rsi

        except Exception as e:
            logger.error(f"计算RSI失败: {e}")
            return 50.0

    async def _detect_market_condition_change(self):
        """检测市场状态变化"""
        try:
            old_condition = self.current_market_condition

            # 基于波动率和趋势判断市场状态
            if self.market_volatility > 0.3:
                if abs(self.market_trend) > 0.05:
                    new_condition = MarketCondition.CRISIS
                else:
                    new_condition = MarketCondition.HIGH_VOLATILITY
            elif self.market_volatility < 0.1:
                new_condition = MarketCondition.LOW_VOLATILITY
            elif self.market_trend > 0.02:
                new_condition = MarketCondition.BULL_MARKET
            elif self.market_trend < -0.02:
                new_condition = MarketCondition.BEAR_MARKET
            else:
                new_condition = MarketCondition.SIDEWAYS

            if new_condition != old_condition:
                self.current_market_condition = new_condition

                # 生成市场状态变化信号
                signal = MarketSignal(
                    signal_id=f"market_condition_{datetime.now().strftime('%Y%m%d_%H%M%S')}",
                    signal_type="market_condition_change",
                    strength=0.9,
                    confidence=0.8,
                    timestamp=datetime.now(),
                    source="market_monitor",
                    data={
                        "old_condition": old_condition.value,
                        "new_condition": new_condition.value,
                        "volatility": self.market_volatility,
                        "trend": self.market_trend
                    }
                )

                self.signal_queue.append(signal)
                logger.info(f"🔄 市场状态变化: {old_condition.value} -> {new_condition.value}")

        except Exception as e:
            logger.error(f"检测市场状态变化失败: {e}")

    async def _generate_market_signals(self):
        """生成市场信号"""
        try:
            current_time = datetime.now()

            # 检查各种信号条件
            for symbol in self.price_data:
                if len(self.price_data[symbol]) < 20:
                    continue

                # 获取指标
                rsi = self.market_indicators.get(f"{symbol}_rsi", 50)
                trend = self.market_indicators.get(f"{symbol}_trend", 0)
                volatility = self.market_indicators.get(f"{symbol}_volatility", 0)

                # RSI超买超卖信号
                if rsi > 80:
                    signal = MarketSignal(
                        signal_id=f"rsi_overbought_{symbol}_{current_time.strftime('%H%M%S')}",
                        signal_type="rsi_overbought",
                        strength=min(1.0, (rsi - 80) / 20),
                        confidence=0.7,
                        timestamp=current_time,
                        source="technical_analysis",
                        data={"symbol": symbol, "rsi": rsi}
                    )
                    self.signal_queue.append(signal)

                elif rsi < 20:
                    signal = MarketSignal(
                        signal_id=f"rsi_oversold_{symbol}_{current_time.strftime('%H%M%S')}",
                        signal_type="rsi_oversold",
                        strength=min(1.0, (20 - rsi) / 20),
                        confidence=0.7,
                        timestamp=current_time,
                        source="technical_analysis",
                        data={"symbol": symbol, "rsi": rsi}
                    )
                    self.signal_queue.append(signal)

                # 趋势突破信号
                if abs(trend) > 0.05:
                    signal_type = "trend_breakout_up" if trend > 0 else "trend_breakout_down"
                    signal = MarketSignal(
                        signal_id=f"{signal_type}_{symbol}_{current_time.strftime('%H%M%S')}",
                        signal_type=signal_type,
                        strength=min(1.0, abs(trend) * 10),
                        confidence=0.8,
                        timestamp=current_time,
                        source="trend_analysis",
                        data={"symbol": symbol, "trend": trend}
                    )
                    self.signal_queue.append(signal)

                # 波动率异常信号
                if volatility > 0.4:
                    signal = MarketSignal(
                        signal_id=f"high_volatility_{symbol}_{current_time.strftime('%H%M%S')}",
                        signal_type="high_volatility_warning",
                        strength=min(1.0, volatility),
                        confidence=0.9,
                        timestamp=current_time,
                        source="volatility_monitor",
                        data={"symbol": symbol, "volatility": volatility}
                    )
                    self.signal_queue.append(signal)

        except Exception as e:
            logger.error(f"生成市场信号失败: {e}")

    async def _trigger_callbacks(self):
        """触发回调函数"""
        try:
            # 处理新信号
            while self.signal_queue:
                signal = self.signal_queue.popleft()

                for callback in self.signal_callbacks:
                    try:
                        if asyncio.iscoroutinefunction(callback):
                            await callback(signal)
                        else:
                            callback(signal)
                    except Exception as e:
                        logger.error(f"信号回调执行失败: {e}")

        except Exception as e:
            logger.error(f"触发回调失败: {e}")

    def add_signal_callback(self, callback: Callable):
        """添加信号回调"""
        self.signal_callbacks.append(callback)

    def get_current_market_state(self) -> Dict[str, Any]:
        """获取当前市场状态"""
        return {
            "market_condition": self.current_market_condition.value,
            "market_volatility": self.market_volatility,
            "market_trend": self.market_trend,
            "indicators": dict(self.market_indicators),
            "signal_count": len(self.signal_queue),
            "last_update": datetime.now().isoformat()
        }

class AdaptiveParameterManager:
    """自适应参数管理器"""

    def __init__(self):
        self.parameters: Dict[str, AdaptiveParameter] = {}
        self.adaptation_rules: Dict[str, Callable] = {}
        self.performance_history: deque = deque(maxlen=1000)

    def register_parameter(self, name: str, initial_value: float,
                          min_value: float, max_value: float,
                          adaptation_rate: float = 0.1):
        """注册自适应参数"""
        self.parameters[name] = AdaptiveParameter(
            name=name,
            current_value=initial_value,
            min_value=min_value,
            max_value=max_value,
            adaptation_rate=adaptation_rate
        )
        logger.info(f"📊 注册自适应参数: {name} = {initial_value}")

    def set_adaptation_rule(self, parameter_name: str, rule_func: Callable):
        """设置适应规则"""
        self.adaptation_rules[parameter_name] = rule_func

    async def adapt_parameters(self, market_state: Dict[str, Any],
                             performance_metrics: Dict[str, float]):
        """适应参数"""
        try:
            # 记录性能
            self.performance_history.append({
                "timestamp": datetime.now(),
                "metrics": performance_metrics.copy(),
                "market_state": market_state.copy()
            })

            # 应用适应规则
            for param_name, rule_func in self.adaptation_rules.items():
                if param_name in self.parameters:
                    try:
                        new_value = rule_func(
                            current_value=self.parameters[param_name].current_value,
                            market_state=market_state,
                            performance_metrics=performance_metrics,
                            history=list(self.performance_history)
                        )

                        old_value = self.parameters[param_name].current_value
                        self.parameters[param_name].update(new_value)

                        if abs(new_value - old_value) > 0.001:
                            logger.info(f"🔧 参数适应: {param_name} {old_value:.4f} -> {new_value:.4f}")

                    except Exception as e:
                        logger.error(f"参数适应规则执行失败 {param_name}: {e}")

        except Exception as e:
            logger.error(f"参数适应失败: {e}")

    def get_parameter_value(self, name: str) -> Optional[float]:
        """获取参数值"""
        return self.parameters[name].current_value if name in self.parameters else None

    def get_all_parameters(self) -> Dict[str, float]:
        """获取所有参数"""
        return {name: param.current_value for name, param in self.parameters.items()}

class RealTimeAdaptiveSystem:
    """实时适应性系统"""

    def __init__(self):
        self.market_monitor = RealTimeMarketMonitor(update_interval=1.0)
        self.parameter_manager = AdaptiveParameterManager()

        # 策略管理
        self.current_strategy = AdaptiveStrategy.BALANCED
        self.strategy_performance: Dict[str, deque] = defaultdict(lambda: deque(maxlen=100))

        # 风险控制
        self.risk_limits = {
            "max_position_size": 0.1,  # 最大仓位
            "max_daily_loss": 0.05,    # 最大日损失
            "max_volatility": 0.3,     # 最大波动率
            "min_liquidity": 1000000   # 最小流动性
        }

        # 异常检测
        self.anomaly_detector = AnomalyDetector()

        # 系统状态
        self.is_running = False
        self.adaptation_task: Optional[asyncio.Task] = None

        # 注册信号回调
        self.market_monitor.add_signal_callback(self._handle_market_signal)

        logger.info("🌐 实时适应性系统初始化完成")

    async def start_system(self):
        """启动系统"""
        if self.is_running:
            return

        self.is_running = True

        # 启动市场监控
        await self.market_monitor.start_monitoring()

        # 启动适应循环
        self.adaptation_task = asyncio.create_task(self._adaptation_loop())

        logger.info("🚀 实时适应性系统已启动")

    async def stop_system(self):
        """停止系统"""
        self.is_running = False

        # 停止市场监控
        await self.market_monitor.stop_monitoring()

        # 停止适应循环
        if self.adaptation_task:
            self.adaptation_task.cancel()
            try:
                await self.adaptation_task
            except asyncio.CancelledError:
                pass

        logger.info("⏹️ 实时适应性系统已停止")

    async def _adaptation_loop(self):
        """适应循环"""
        try:
            while self.is_running:
                # 获取市场状态
                market_state = self.market_monitor.get_current_market_state()

                # 计算性能指标
                performance_metrics = await self._calculate_performance_metrics()

                # 适应参数
                await self.parameter_manager.adapt_parameters(market_state, performance_metrics)

                # 适应策略
                await self._adapt_strategy(market_state, performance_metrics)

                # 检测异常
                await self._detect_anomalies(market_state, performance_metrics)

                # 更新风险限制
                await self._update_risk_limits(market_state)

                await asyncio.sleep(5.0)  # 5秒适应周期

        except asyncio.CancelledError:
            logger.info("适应循环被取消")
        except Exception as e:
            logger.error(f"适应循环异常: {e}")

    async def _handle_market_signal(self, signal: MarketSignal):
        """处理市场信号"""
        try:
            logger.info(f"📡 收到市场信号: {signal.signal_type} (强度: {signal.strength:.2f})")

            # 根据信号类型采取行动
            if signal.signal_type == "market_condition_change":
                await self._handle_market_condition_change(signal)
            elif signal.signal_type in ["rsi_overbought", "rsi_oversold"]:
                await self._handle_rsi_signal(signal)
            elif "trend_breakout" in signal.signal_type:
                await self._handle_trend_breakout(signal)
            elif signal.signal_type == "high_volatility_warning":
                await self._handle_volatility_warning(signal)

        except Exception as e:
            logger.error(f"处理市场信号失败: {e}")

    async def _handle_market_condition_change(self, signal: MarketSignal):
        """处理市场状态变化"""
        new_condition = signal.data.get("new_condition")

        # 根据市场状态调整策略
        if new_condition == MarketCondition.BULL_MARKET.value:
            self.current_strategy = AdaptiveStrategy.MOMENTUM
        elif new_condition == MarketCondition.BEAR_MARKET.value:
            self.current_strategy = AdaptiveStrategy.DEFENSIVE
        elif new_condition == MarketCondition.HIGH_VOLATILITY.value:
            self.current_strategy = AdaptiveStrategy.MEAN_REVERSION
        elif new_condition == MarketCondition.CRISIS.value:
            self.current_strategy = AdaptiveStrategy.DEFENSIVE
            # 紧急风险控制
            self.risk_limits["max_position_size"] *= 0.5
        else:
            self.current_strategy = AdaptiveStrategy.BALANCED

        logger.info(f"🔄 策略调整: {self.current_strategy.value}")

    async def _calculate_performance_metrics(self) -> Dict[str, float]:
        """计算性能指标"""
        try:
            # 这里应该计算真实的性能指标
            # 为了演示，我们返回模拟指标
            return {
                "total_return": np.random.normal(0.001, 0.02),  # 日收益率
                "sharpe_ratio": np.random.normal(1.5, 0.5),
                "max_drawdown": np.random.uniform(0.01, 0.05),
                "win_rate": np.random.uniform(0.4, 0.7),
                "profit_factor": np.random.uniform(1.0, 2.0)
            }

        except Exception as e:
            logger.error(f"计算性能指标失败: {e}")
            return {}

    def get_system_status(self) -> Dict[str, Any]:
        """获取系统状态"""
        return {
            "is_running": self.is_running,
            "current_strategy": self.current_strategy.value,
            "market_state": self.market_monitor.get_current_market_state(),
            "parameters": self.parameter_manager.get_all_parameters(),
            "risk_limits": self.risk_limits.copy(),
            "system_time": datetime.now().isoformat()
        }

class AnomalyDetector:
    """异常检测器"""

    def __init__(self):
        self.baseline_metrics: Dict[str, float] = {}
        self.anomaly_threshold = 3.0  # 3倍标准差

    async def detect_anomalies(self, current_metrics: Dict[str, float]) -> List[str]:
        """检测异常"""
        anomalies = []

        for metric_name, current_value in current_metrics.items():
            if metric_name in self.baseline_metrics:
                baseline = self.baseline_metrics[metric_name]
                deviation = abs(current_value - baseline) / max(abs(baseline), 0.001)

                if deviation > self.anomaly_threshold:
                    anomalies.append(f"{metric_name}: {deviation:.2f}倍偏差")

        return anomalies

    def update_baseline(self, metrics: Dict[str, float]):
        """更新基线"""
        for metric_name, value in metrics.items():
            if metric_name in self.baseline_metrics:
                # 指数移动平均
                alpha = 0.1
                self.baseline_metrics[metric_name] = (
                    alpha * value + (1 - alpha) * self.baseline_metrics[metric_name]
                )
            else:
                self.baseline_metrics[metric_name] = value

# 全局实时适应性系统实例
real_time_adaptive_system = RealTimeAdaptiveSystem()

__all__ = [
    "RealTimeAdaptiveSystem",
    "RealTimeMarketMonitor",
    "AdaptiveParameterManager",
    "MarketSignal",
    "MarketCondition",
    "AdaptiveStrategy",
    "real_time_adaptive_system"
]