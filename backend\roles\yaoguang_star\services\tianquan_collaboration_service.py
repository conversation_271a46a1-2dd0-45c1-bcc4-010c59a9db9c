#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
瑶光星与天权星协作服务
实现真正的自动化学习流程
"""

import asyncio
import logging
import aiohttp
from datetime import datetime
from typing import Dict, List, Optional, Any
import json

logger = logging.getLogger(__name__)

class TianquanCollaborationService:
    """瑶光星与天权星统一协作服务"""

    def __init__(self):
        self.service_name = "瑶光星与天权星统一协作服务"
        self.version = "1.0.0"
        self.tianquan_api_base = "http://127.0.0.1:8002/api/commander"
        self.session = None
        self.active_collaborations = {}

        # 天权星战法库（合并自战法学习服务）
        self.tianquan_strategies = {
            "trend_following": {
                "name": "趋势跟踪战法",
                "description": "基于移动平均线和趋势指标的跟踪策略",
                "parameters": {
                    "ma_short": 5,
                    "ma_long": 20,
                    "trend_threshold": 0.02
                },
                "success_rate": 0.0,
                "avg_return": 0.0,
                "max_drawdown": 0.0,
                "test_count": 0
            },
            "mean_reversion": {
                "name": "均值回归战法",
                "description": "基于价格偏离均值的回归策略",
                "parameters": {
                    "lookback_period": 20,
                    "deviation_threshold": 2.0,
                    "exit_threshold": 0.5
                },
                "success_rate": 0.0,
                "avg_return": 0.0,
                "max_drawdown": 0.0,
                "test_count": 0
            }
        }

        logger.info(f"✅ {self.service_name} v{self.version} 初始化完成")
    
    async def __aenter__(self):
        """异步上下文管理器入口"""
        self.session = aiohttp.ClientSession(
            timeout=aiohttp.ClientTimeout(total=30)
        )
        return self
    
    async def __aexit__(self, exc_type, exc_val, exc_tb):
        """异步上下文管理器出口"""
        if self.session:
            await self.session.close()
    
    async def start_research_collaboration(self, session_data: Dict) -> Dict[str, Any]:
        """启动研究模式协作"""
        
        try:
            logger.info(f"🚀 启动研究模式协作: {session_data.get('session_id')}")
            
            # 1. 准备协作任务数据
            collaboration_task = {
                "task_type": "stock_research",
                "session_id": session_data.get("session_id"),
                "stock_code": session_data.get("stock_code"),
                "stock_name": session_data.get("stock_name"),
                "analysis_periods": session_data.get("target_periods", []),
                "learning_mode": "research",
                "collaboration_config": {
                    "auto_analysis": True,
                    "four_stars_required": True,
                    "analysis_depth": "comprehensive",
                    "time_acceleration": 10.0
                },
                "expected_outputs": [
                    "trend_analysis",
                    "decision_recommendations", 
                    "risk_assessment",
                    "profit_optimization"
                ]
            }
            
            # 2. 发送任务给天权星
            tianquan_result = await self._send_task_to_tianquan(collaboration_task)
            
            if tianquan_result.get("success"):
                # 3. 启动自动化协作流程
                collaboration_id = tianquan_result.get("collaboration_id")
                
                # 记录协作状态
                self.active_collaborations[collaboration_id] = {
                    "session_id": session_data.get("session_id"),
                    "task_type": "research",
                    "status": "active",
                    "start_time": datetime.now(),
                    "tianquan_task_id": tianquan_result.get("task_id"),
                    "progress": []
                }
                
                # 4. 启动进度监控
                asyncio.create_task(self._monitor_collaboration_progress(collaboration_id))
                
                logger.info(f"✅ 研究协作启动成功: {collaboration_id}")
                
                return {
                    "success": True,
                    "collaboration_id": collaboration_id,
                    "tianquan_response": tianquan_result,
                    "auto_process_started": True,
                    "message": "天权星已接收任务，自动化分析流程已启动"
                }
            else:
                logger.error(f"❌ 天权星任务发送失败: {tianquan_result.get('error')}")
                return {
                    "success": False,
                    "error": "天权星任务发送失败",
                    "details": tianquan_result
                }
        
        except Exception as e:
            logger.error(f"启动研究协作失败: {e}")
            return {
                "success": False,
                "error": str(e)
            }
    
    async def start_practice_collaboration(self, session_data: Dict) -> Dict[str, Any]:
        """启动练习模式协作"""
        
        try:
            logger.info(f"🎮 启动练习模式协作: {session_data.get('session_id')}")
            
            # 1. 准备练习任务数据
            collaboration_task = {
                "task_type": "stock_practice",
                "session_id": session_data.get("session_id"),
                "stock_code": session_data.get("stock_code"),
                "stock_name": session_data.get("stock_name"),
                "practice_period": {
                    "start_date": session_data.get("start_date"),
                    "end_date": session_data.get("end_date")
                },
                "blind_mode": session_data.get("blind_mode", True),
                "learning_mode": "practice",
                "collaboration_config": {
                    "auto_decision": True,
                    "real_time_feedback": True,
                    "time_acceleration": 5.0,
                    "evaluation_mode": True
                },
                "expected_outputs": [
                    "buy_sell_decisions",
                    "position_management",
                    "risk_control",
                    "performance_evaluation"
                ]
            }
            
            # 2. 发送任务给天权星
            tianquan_result = await self._send_task_to_tianquan(collaboration_task)
            
            if tianquan_result.get("success"):
                # 3. 启动自动化练习流程
                collaboration_id = tianquan_result.get("collaboration_id")
                
                # 记录协作状态
                self.active_collaborations[collaboration_id] = {
                    "session_id": session_data.get("session_id"),
                    "task_type": "practice",
                    "status": "active",
                    "start_time": datetime.now(),
                    "tianquan_task_id": tianquan_result.get("task_id"),
                    "progress": []
                }
                
                # 4. 启动进度监控
                asyncio.create_task(self._monitor_collaboration_progress(collaboration_id))
                
                logger.info(f"✅ 练习协作启动成功: {collaboration_id}")
                
                return {
                    "success": True,
                    "collaboration_id": collaboration_id,
                    "tianquan_response": tianquan_result,
                    "auto_process_started": True,
                    "message": "天权星已接收任务，自动化练习流程已启动"
                }
            else:
                logger.error(f"❌ 天权星练习任务发送失败: {tianquan_result.get('error')}")
                return {
                    "success": False,
                    "error": "天权星练习任务发送失败",
                    "details": tianquan_result
                }
        
        except Exception as e:
            logger.error(f"启动练习协作失败: {e}")
            return {
                "success": False,
                "error": str(e)
            }
    
    async def _send_task_to_tianquan(self, task_data: Dict) -> Dict[str, Any]:
        """发送任务给天权星"""
        
        try:
            if not self.session:
                # 如果没有session，创建临时的
                async with aiohttp.ClientSession() as temp_session:
                    return await self._make_tianquan_request(temp_session, task_data)
            else:
                return await self._make_tianquan_request(self.session, task_data)
        
        except Exception as e:
            logger.error(f"发送任务给天权星失败: {e}")
            return {
                "success": False,
                "error": str(e)
            }
    
    async def _make_tianquan_request(self, session: aiohttp.ClientSession, task_data: Dict) -> Dict[str, Any]:
        """实际发送HTTP请求给天权星"""
        
        try:
            url = f"{self.tianquan_api_base}/make_decision"
            
            # 转换为天权星API期望的格式
            request_data = {
                "stock_code": task_data.get("stock_code"),
                "market_context": {
                    "task_type": task_data.get("task_type"),
                    "learning_mode": task_data.get("learning_mode"),
                    "session_id": task_data.get("session_id"),
                    "collaboration_config": task_data.get("collaboration_config", {}),
                    "analysis_periods": task_data.get("analysis_periods", []),
                    "practice_period": task_data.get("practice_period", {}),
                    "expected_outputs": task_data.get("expected_outputs", [])
                },
                "risk_preference": "moderate",
                "auto_collaboration": True
            }
            
            logger.info(f"📡 发送请求到天权星: {url}")
            
            async with session.post(url, json=request_data) as response:
                if response.status == 200:
                    result = await response.json()
                    
                    logger.info(f"✅ 天权星响应成功: {result.get('message', 'OK')}")
                    
                    # 生成协作ID
                    collaboration_id = f"collab_{task_data.get('session_id')}_{datetime.now().strftime('%Y%m%d_%H%M%S')}"
                    
                    return {
                        "success": True,
                        "collaboration_id": collaboration_id,
                        "task_id": result.get("data", {}).get("decision", {}).get("decision_time", ""),
                        "tianquan_decision": result.get("data", {}),
                        "message": "天权星已接收任务并开始处理"
                    }
                else:
                    error_text = await response.text()
                    logger.error(f"❌ 天权星API错误: {response.status} - {error_text}")
                    
                    return {
                        "success": False,
                        "error": f"天权星API错误: {response.status}",
                        "details": error_text
                    }
        
        except aiohttp.ClientError as e:
            logger.error(f"❌ 网络请求失败: {e}")
            return {
                "success": False,
                "error": f"网络请求失败: {str(e)}"
            }
        except Exception as e:
            logger.error(f"❌ 请求处理失败: {e}")
            return {
                "success": False,
                "error": f"请求处理失败: {str(e)}"
            }
    
    async def _monitor_collaboration_progress(self, collaboration_id: str):
        """监控协作进度"""
        
        try:
            collaboration = self.active_collaborations.get(collaboration_id)
            if not collaboration:
                return
            
            logger.info(f"📊 开始监控协作进度: {collaboration_id}")
            
            # 模拟协作进度更新
            progress_steps = [
                {"step": "task_received", "message": "天权星已接收任务", "progress": 10},
                {"step": "four_stars_notified", "message": "四星协作通知已发送", "progress": 25},
                {"step": "tianji_risk_analysis", "message": "天玑星风险分析进行中", "progress": 40},
                {"step": "tianxuan_technical", "message": "天璇星技术分析进行中", "progress": 55},
                {"step": "tianshu_sentiment", "message": "天枢星情绪分析进行中", "progress": 70},
                {"step": "kaiyang_evaluation", "message": "开阳星综合评估进行中", "progress": 85},
                {"step": "tianquan_decision", "message": "天权星综合决策制定中", "progress": 95},
                {"step": "collaboration_complete", "message": "协作流程完成", "progress": 100}
            ]
            
            for step_data in progress_steps:
                # 更新进度
                collaboration["progress"].append({
                    "timestamp": datetime.now().isoformat(),
                    "step": step_data["step"],
                    "message": step_data["message"],
                    "progress": step_data["progress"]
                })
                
                logger.info(f"📈 协作进度更新: {step_data['message']} ({step_data['progress']}%)")
                
                # 模拟处理时间
                await asyncio.sleep(2)
            
            # 标记协作完成
            collaboration["status"] = "completed"
            collaboration["end_time"] = datetime.now()
            
            logger.info(f"✅ 协作流程完成: {collaboration_id}")
            
        except Exception as e:
            logger.error(f"监控协作进度失败: {e}")
    
    async def get_collaboration_status(self, collaboration_id: str) -> Dict[str, Any]:
        """获取协作状态"""
        
        try:
            collaboration = self.active_collaborations.get(collaboration_id)
            
            if not collaboration:
                return {
                    "success": False,
                    "error": "协作不存在"
                }
            
            return {
                "success": True,
                "collaboration_id": collaboration_id,
                "status": collaboration["status"],
                "task_type": collaboration["task_type"],
                "session_id": collaboration["session_id"],
                "start_time": collaboration["start_time"].isoformat(),
                "progress": collaboration["progress"],
                "current_progress": collaboration["progress"][-1] if collaboration["progress"] else None
            }
        
        except Exception as e:
            logger.error(f"获取协作状态失败: {e}")
            return {
                "success": False,
                "error": str(e)
            }
    
    async def get_all_active_collaborations(self) -> Dict[str, Any]:
        """获取所有活跃协作"""
        
        try:
            active_list = []
            
            for collab_id, collab_data in self.active_collaborations.items():
                active_list.append({
                    "collaboration_id": collab_id,
                    "session_id": collab_data["session_id"],
                    "task_type": collab_data["task_type"],
                    "status": collab_data["status"],
                    "start_time": collab_data["start_time"].isoformat(),
                    "current_progress": collab_data["progress"][-1] if collab_data["progress"] else None
                })
            
            return {
                "success": True,
                "active_collaborations": active_list,
                "total_count": len(active_list)
            }
        
        except Exception as e:
            logger.error(f"获取活跃协作失败: {e}")
            return {
                "success": False,
                "error": str(e)
            }

# 全局实例
tianquan_collaboration_service = TianquanCollaborationService()
