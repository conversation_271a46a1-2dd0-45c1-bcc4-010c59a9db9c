#!/usr/bin/env python3
"""
测试瑶光星真实智能体功能
检查瑶光星的实际智能体应用情况
"""

import sys
import asyncio
sys.path.append('.')

async def test_yaoguang_real_agent():
    """测试瑶光星真实智能体"""
    try:
        print('🌟' + '='*80)
        print('🌟 瑶光星真实智能体功能测试')
        print('🌟' + '='*80)
        
        # 1. 测试瑶光星服务
        print('\n📋 1. 测试瑶光星主服务...')
        try:
            from roles.yaoguang_star.yaoguang_star_service import yaoguang_star_service
            print(f'✅ 瑶光星服务: {yaoguang_star_service.service_name} v{yaoguang_star_service.version}')
            print(f'   智能等级: {yaoguang_star_service.intelligence_level}')
            print(f'   自主模式: {yaoguang_star_service.autonomous_mode}')
            
            # 测试智能分析功能
            test_data = {
                "market_data": {"trend": "bullish", "volatility": 0.15},
                "analysis_request": "市场趋势分析"
            }
            
            analysis_result = await yaoguang_star_service.intelligent_analysis(test_data, "market_analysis")
            if analysis_result.get('success'):
                print(f'✅ 智能分析功能正常')
                print(f'   分析结果: {analysis_result.get("analysis_result", {}).get("specialized_insights", {})}')
            else:
                print(f'❌ 智能分析功能异常: {analysis_result.get("error")}')
                
        except Exception as e:
            print(f'❌ 瑶光星服务测试失败: {e}')
        
        # 2. 测试独立智能体
        print('\n🤖 2. 测试瑶光星独立智能体...')
        try:
            from roles.yaoguang_star.independent_yaoguang_agent import YaoguangIndependentAgent
            
            yaoguang_agent = YaoguangIndependentAgent()
            print(f'✅ 独立智能体: {yaoguang_agent.agent_name}')
            print(f'   学习算法: {len(yaoguang_agent.learning_algorithms)}种')
            print(f'   数据源: {len(yaoguang_agent.data_sources)}个')
            print(f'   研究领域: {len(yaoguang_agent.research_areas)}个')
            
            # 获取智能体状态
            agent_state = await yaoguang_agent.get_agent_state()
            print(f'   当前阶段: {agent_state.get("current_phase")}')
            print(f'   性能得分: {agent_state.get("performance_score", 0):.2f}')
            
            # 测试智能体思考
            thinking_result = await yaoguang_agent.think_about("如何优化量化策略的学习效果")
            if thinking_result.get('success'):
                print(f'✅ 智能体思考功能正常')
                thought = thinking_result.get('thought', {})
                print(f'   思考内容: {thought.get("content", "")[:100]}...')
                print(f'   置信度: {thought.get("confidence", 0):.2f}')
            else:
                print(f'❌ 智能体思考功能异常: {thinking_result.get("error")}')
                
        except Exception as e:
            print(f'❌ 独立智能体测试失败: {e}')
        
        # 3. 测试协调服务
        print('\n🎯 3. 测试瑶光星协调服务...')
        try:
            from roles.yaoguang_star.yaoguang_coordination_service import yaoguang_coordination_service
            
            print(f'✅ 协调服务: {yaoguang_coordination_service.service_name} v{yaoguang_coordination_service.version}')
            
            # 获取协调状态
            coord_status = await yaoguang_coordination_service.get_coordination_status()
            service_info = coord_status.get("service_info", {})
            print(f'   运行时间: {service_info.get("uptime", "未知")}')
            
            coord_status_info = coord_status.get("coordination_status", {})
            print(f'   活跃协调: {coord_status_info.get("active_coordinations", 0)}')
            print(f'   已完成协调: {coord_status_info.get("completed_coordinations", 0)}')
            
            # 检查协调引擎
            engines = {
                "七星协调器": yaoguang_coordination_service.seven_star_coordinator,
                "学习协调器": yaoguang_coordination_service.learning_coordinator,
                "回测协调器": yaoguang_coordination_service.backtest_coordinator,
                "反馈引擎": yaoguang_coordination_service.feedback_engine
            }
            
            print(f'\n   🔧 协调引擎状态:')
            for name, engine in engines.items():
                engine_name = getattr(engine, 'coordinator_name', getattr(engine, 'engine_name', '未知'))
                version = getattr(engine, 'version', '未知')
                print(f'     {name}: {engine_name} v{version}')
                
        except Exception as e:
            print(f'❌ 协调服务测试失败: {e}')
        
        # 4. 检查实际参与的星数
        print('\n🔍 4. 检查实际参与协调的星数...')
        try:
            from roles.yaoguang_star.core.learning_flow_coordinator import learning_flow_coordinator
            from roles.yaoguang_star.core.backtest_flow_coordinator import backtest_flow_coordinator
            
            # 检查学习协调器的星际能力
            learning_capabilities = learning_flow_coordinator.star_learning_capabilities
            print(f'✅ 学习协调器支持的星: {len(learning_capabilities)}个')
            for star, capabilities in learning_capabilities.items():
                specialties = capabilities.get('specialties', [])
                print(f'   {star}: {len(specialties)}个专长 - {specialties[0] if specialties else "无"}')
            
            # 检查回测协调器的星际能力
            backtest_capabilities = backtest_flow_coordinator.star_backtest_capabilities
            print(f'\n✅ 回测协调器支持的星: {len(backtest_capabilities)}个')
            for star, capabilities in backtest_capabilities.items():
                functions = capabilities.get('backtest_functions', [])
                print(f'   {star}: {len(functions)}个功能 - {functions[0] if functions else "无"}')
                
        except Exception as e:
            print(f'❌ 星数检查失败: {e}')
        
        # 5. 测试实际协调功能
        print('\n🚀 5. 测试实际协调功能...')
        try:
            # 简单的学习协调测试
            learning_config = {
                "learning_focus": ["pattern_recognition"],
                "learning_mode": "focused",
                "use_detailed_flow": False,  # 使用简单流程
                "priority": "low",
                "requester": "test"
            }
            
            print('   启动学习协调测试...')
            learning_result = await yaoguang_coordination_service.coordinate_learning_session(learning_config)
            
            if learning_result.get('success'):
                print(f'✅ 学习协调测试成功')
                print(f'   请求ID: {learning_result["request_id"]}')
                print(f'   执行时间: {learning_result["execution_time"]:.2f}秒')
                print(f'   使用协调器: {learning_result["coordinator_used"]}')
            else:
                print(f'❌ 学习协调测试失败: {learning_result.get("error")}')
                
        except Exception as e:
            print(f'❌ 协调功能测试失败: {e}')
        
        # 6. 检查智能体的实际应用
        print('\n🧠 6. 检查智能体的实际应用情况...')
        try:
            # 检查是否有真实的智能体决策记录
            from roles.yaoguang_star.core.core_systems_integration import core_systems_integration
            
            # 搜索瑶光星的记忆
            memories = await core_systems_integration.search_memories("学习", limit=5)
            print(f'✅ 瑶光星记忆系统: {len(memories)}条记录')
            
            if memories:
                for i, memory in enumerate(memories[:3], 1):
                    content = memory.get('content', '')[:50]
                    timestamp = memory.get('timestamp', '未知')
                    print(f'   {i}. {content}... ({timestamp})')
            else:
                print('   暂无记忆记录')
                
            # 测试DeepSeek响应
            deepseek_result = await core_systems_integration.generate_deepseek_response(
                "瑶光星的核心职责是什么？", "learning", 200
            )
            
            if deepseek_result.get('success'):
                print(f'✅ DeepSeek智能响应正常')
                content = deepseek_result.get('content', '')[:100]
                print(f'   响应内容: {content}...')
            else:
                print(f'❌ DeepSeek响应异常: {deepseek_result.get("error")}')
                
        except Exception as e:
            print(f'❌ 智能体应用检查失败: {e}')
        
        print('\n' + '='*80)
        print('🎉 瑶光星真实智能体功能测试完成！')
        print('='*80)
        
    except Exception as e:
        print(f'❌ 测试执行失败: {e}')
        import traceback
        traceback.print_exc()

if __name__ == "__main__":
    asyncio.run(test_yaoguang_real_agent())
