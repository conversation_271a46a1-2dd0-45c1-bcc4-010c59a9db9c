#!/usr/bin/env python3
"""
测试6星协调功能
验证瑶光星是否真的协调了6颗星
"""

import sys
import asyncio
sys.path.append('.')

async def test_six_stars_coordination():
    """测试6星协调功能"""
    try:
        print('🌟' + '='*80)
        print('🌟 测试瑶光星6星协调功能')
        print('🌟' + '='*80)
        
        from roles.yaoguang_star.yaoguang_coordination_service import yaoguang_coordination_service
        
        # 测试详细学习流程（应该包含6星）
        print('\n🎓 测试详细学习协调流程（6星参与）...')
        learning_config = {
            "learning_focus": ["pattern_recognition", "risk_assessment"],
            "learning_mode": "comprehensive",
            "use_detailed_flow": True,  # 使用详细流程
            "priority": "high",
            "requester": "six_stars_test",
            "objectives": {
                "accuracy_target": 0.85,
                "risk_control": 0.10
            }
        }
        
        print(f'   配置: 使用详细流程 = {learning_config["use_detailed_flow"]}')
        print(f'   学习重点: {learning_config["learning_focus"]}')
        
        learning_result = await yaoguang_coordination_service.coordinate_learning_session(learning_config)
        
        if learning_result.get('success'):
            print('✅ 详细学习协调成功！')
            print(f'   请求ID: {learning_result["request_id"]}')
            print(f'   执行时间: {learning_result["execution_time"]:.2f}秒')
            print(f'   使用协调器: {learning_result["coordinator_used"]}')
            
            coord_result = learning_result['coordination_result']
            if coord_result.get('success'):
                learning_res = coord_result.get('learning_result', {})
                if learning_res.get('success'):
                    print(f'\n📊 详细学习结果:')
                    print(f'   目标数量: {coord_result.get("objectives_count", 0)}')
                    print(f'   目标达成: {learning_res.get("objectives_achieved", 0)}')
                    print(f'   知识获得: {learning_res.get("knowledge_gained", 0)}')
                    
                    # 检查各阶段参与的星数
                    learning_results = learning_res.get('learning_results', {})
                    print(f'\n🔍 各阶段参与星数分析:')
                    
                    for stage, stage_result in learning_results.items():
                        print(f'   {stage}:')
                        if stage_result.get('success'):
                            if stage == 'analysis':
                                stars_completed = stage_result.get('stars_completed', 0)
                                total_patterns = stage_result.get('total_patterns_learned', 0)
                                print(f'     ✅ 成功 - 参与星数: {stars_completed}, 学习模式: {total_patterns}')
                                
                                # 如果是6星，应该有更多的模式
                                if stars_completed >= 6:
                                    print(f'     🎉 确认6星参与！')
                                elif stars_completed >= 4:
                                    print(f'     ⚠️  只有{stars_completed}星参与，未达到6星')
                                else:
                                    print(f'     ❌ 参与星数过少: {stars_completed}')
                            else:
                                print(f'     ✅ 成功')
                        else:
                            print(f'     ❌ 失败')
        else:
            print(f'❌ 详细学习协调失败: {learning_result.get("error")}')
        
        # 测试回测协调（应该包含6星）
        print('\n🔄 测试回测协调流程（6星参与）...')
        backtest_config = {
            "strategy_name": "6星协调测试策略",
            "start_date": "2024-01-01",
            "end_date": "2024-03-31",  # 缩短时间以加快测试
            "initial_capital": 1000000.0,
            "benchmark": "000300.XSHG",
            "universe_size": 50,
            "rebalance_frequency": "monthly",
            "commission_rate": 0.0003,
            "slippage_rate": 0.001,
            "max_position_size": 0.1,
            "priority": "high",
            "requester": "six_stars_test"
        }
        
        print(f'   策略: {backtest_config["strategy_name"]}')
        print(f'   期间: {backtest_config["start_date"]} 至 {backtest_config["end_date"]}')
        
        backtest_result = await yaoguang_coordination_service.coordinate_backtest_session(backtest_config)
        
        if backtest_result.get('success'):
            print('✅ 回测协调成功！')
            print(f'   请求ID: {backtest_result["request_id"]}')
            print(f'   执行时间: {backtest_result["execution_time"]:.2f}秒')
            
            coord_result = backtest_result['coordination_result']
            if coord_result.get('success'):
                backtest_res = coord_result.get('backtest_result', {})
                if backtest_res.get('success'):
                    print(f'\n📊 回测结果:')
                    print(f'   时间线点数: {backtest_res.get("timeline_points", 0)}')
                    
                    # 检查各阶段参与的星数
                    backtest_results = backtest_res.get('backtest_results', {})
                    print(f'\n🔍 回测各阶段参与星数分析:')
                    
                    for stage, stage_result in backtest_results.items():
                        print(f'   {stage}:')
                        if stage_result.get('success'):
                            if stage == 'historical_analysis':
                                stars_completed = stage_result.get('stars_completed', 0)
                                historical_decisions = stage_result.get('total_historical_decisions', 0)
                                print(f'     ✅ 成功 - 参与星数: {stars_completed}, 历史决策: {historical_decisions}')
                                
                                # 检查是否真的有6星参与
                                if stars_completed >= 6:
                                    print(f'     🎉 确认6星参与回测！')
                                elif stars_completed >= 4:
                                    print(f'     ⚠️  只有{stars_completed}星参与，未达到6星')
                                else:
                                    print(f'     ❌ 参与星数过少: {stars_completed}')
                            else:
                                print(f'     ✅ 成功')
                        else:
                            print(f'     ❌ 失败')
        else:
            print(f'❌ 回测协调失败: {backtest_result.get("error")}')
        
        # 检查协调器配置
        print('\n🔧 检查协调器配置...')
        from roles.yaoguang_star.core.learning_flow_coordinator import learning_flow_coordinator
        from roles.yaoguang_star.core.backtest_flow_coordinator import backtest_flow_coordinator
        
        print(f'学习协调器支持星数: {len(learning_flow_coordinator.star_learning_capabilities)}')
        print(f'回测协调器支持星数: {len(backtest_flow_coordinator.star_backtest_capabilities)}')
        
        print(f'\n📋 学习协调器支持的星:')
        for star in learning_flow_coordinator.star_learning_capabilities.keys():
            print(f'   - {star}')
            
        print(f'\n📋 回测协调器支持的星:')
        for star in backtest_flow_coordinator.star_backtest_capabilities.keys():
            print(f'   - {star}')
        
        # 获取最终协调状态
        print('\n📈 最终协调状态:')
        final_status = await yaoguang_coordination_service.get_coordination_status()
        
        session_stats = final_status.get("session_statistics", {})
        print(f'   学习会话: {session_stats.get("learning", 0)}')
        print(f'   回测会话: {session_stats.get("backtest", 0)}')
        
        perf_metrics = final_status.get("performance_metrics", {})
        print(f'   总协调数: {perf_metrics.get("total_coordinations", 0)}')
        print(f'   成功协调数: {perf_metrics.get("successful_coordinations", 0)}')
        
        print('\n' + '='*80)
        print('🎉 6星协调功能测试完成！')
        print('='*80)
        
    except Exception as e:
        print(f'❌ 测试执行失败: {e}')
        import traceback
        traceback.print_exc()

if __name__ == "__main__":
    asyncio.run(test_six_stars_coordination())
