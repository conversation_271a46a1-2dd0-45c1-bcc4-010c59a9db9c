"""
风险评估模块 - 实时评估每个建议的风险等级
基于真实市场数据和用户组合的风险计算
"""

import asyncio
import sqlite3
import numpy as np
import pandas as pd
from typing import Dict, List, Optional, Any, Tuple
from dataclasses import dataclass
from enum import Enum
from datetime import datetime, timedelta
import json

from services.realtime_data_pipeline import (
    realtime_data_pipeline, RealTimeMarketData, UserPortfolioData
)

class RiskLevel(Enum):
    """风险等级"""
    VERY_LOW = "very_low"
    LOW = "low"
    MEDIUM = "medium"
    HIGH = "high"
    VERY_HIGH = "very_high"

@dataclass
class RiskAssessment:
    """风险评估结果"""
    overall_risk_level: RiskLevel
    risk_score: float  # 0-100
    risk_factors: List[Dict[str, Any]]
    recommendations: List[str]
    max_position_size: float
    stop_loss_price: Optional[float]
    take_profit_price: Optional[float]
    confidence: float

@dataclass
class MarketRiskMetrics:
    """市场风险指标"""
    volatility: float
    beta: float
    correlation_with_market: float
    liquidity_risk: float
    sector_concentration: float

class RiskAssessmentEngine:
    """风险评估引擎"""
    
    def __init__(self):
        self.db_path = "backend/data/stock_master.db"
        self.realtime_db_path = "backend/data/stock_realtime.db"
        
        # 风险参数配置
        self.risk_config = {
            "volatility_thresholds": {
                "very_low": 0.1,
                "low": 0.2,
                "medium": 0.3,
                "high": 0.4,
                "very_high": 0.5
            },
            "position_limits": {
                "single_stock": 0.1,      # 单股最大10%
                "sector": 0.3,            # 单行业最大30%
                "total_equity": 0.8       # 股票总仓位最大80%
            },
            "stop_loss_rates": {
                "conservative": 0.05,     # 保守5%
                "moderate": 0.08,         # 适中8%
                "aggressive": 0.12        # 激进12%
            }
        }
        
        # 市场基准数据
        self.market_benchmark = {
            "index_symbol": "000001",  # 上证指数
            "risk_free_rate": 0.03        # 无风险利率3%
        }
    
    async def assess_trading_risk(self, 
                                symbol: str, 
                                action: str,  # buy, sell
                                quantity: int,
                                price: float,
                                user_id: str) -> RiskAssessment:
        """评估交易风险"""
        try:
            # 1. 获取市场数据
            market_data = await realtime_data_pipeline.get_realtime_market_data([symbol])
            current_data = market_data.get(symbol)
            
            if not current_data:
                return self._create_high_risk_assessment("无法获取股票数据")
            
            # 2. 获取用户组合数据
            portfolio_data = await realtime_data_pipeline.get_user_portfolio_data(user_id)
            
            # 3. 计算市场风险指标
            market_risk = await self._calculate_market_risk_metrics(symbol)
            
            # 4. 计算组合风险
            portfolio_risk = self._calculate_portfolio_risk(portfolio_data, symbol, action, quantity, price)
            
            # 5. 计算流动性风险
            liquidity_risk = self._calculate_liquidity_risk(current_data)
            
            # 6. 计算技术风险
            technical_risk = await self._calculate_technical_risk(symbol)
            
            # 7. 综合风险评估
            overall_risk = self._calculate_overall_risk(
                market_risk, portfolio_risk, liquidity_risk, technical_risk
            )
            
            # 8. 生成风险建议
            recommendations = self._generate_risk_recommendations(
                overall_risk, market_risk, portfolio_risk, action
            )
            
            # 9. 计算建议仓位和止损价格
            max_position_size = self._calculate_max_position_size(
                portfolio_data, overall_risk.risk_score
            )
            
            stop_loss_price = self._calculate_stop_loss_price(
                current_data.price, action, overall_risk.risk_score
            )
            
            take_profit_price = self._calculate_take_profit_price(
                current_data.price, action, overall_risk.risk_score
            )
            
            return RiskAssessment(
                overall_risk_level=overall_risk.risk_level,
                risk_score=overall_risk.risk_score,
                risk_factors=overall_risk.risk_factors,
                recommendations=recommendations,
                max_position_size=max_position_size,
                stop_loss_price=stop_loss_price,
                take_profit_price=take_profit_price,
                confidence=overall_risk.confidence
            )
            
        except Exception as e:
            print(f"❌ 风险评估失败: {e}")
            return self._create_high_risk_assessment(f"风险评估异常: {str(e)}")
    
    async def _calculate_market_risk_metrics(self, symbol: str) -> MarketRiskMetrics:
        """计算市场风险指标"""
        try:
            # 获取历史价格数据
            historical_prices = await self._get_historical_prices(symbol, days=60)
            market_prices = await self._get_historical_prices(self.market_benchmark["index_symbol"], days=60)
            
            if len(historical_prices) < 20 or len(market_prices) < 20:
                # 数据不足，返回默认值
                return MarketRiskMetrics(
                    volatility=0.25,
                    beta=1.0,
                    correlation_with_market=0.5,
                    liquidity_risk=0.3,
                    sector_concentration=0.2
                )
            
            # 计算收益率
            stock_returns = np.diff(historical_prices) / historical_prices[:-1]
            market_returns = np.diff(market_prices) / market_prices[:-1]
            
            # 计算波动率
            volatility = np.std(stock_returns) * np.sqrt(252)  # 年化波动率
            
            # 计算Beta
            covariance = np.cov(stock_returns, market_returns)[0, 1]
            market_variance = np.var(market_returns)
            beta = covariance / market_variance if market_variance > 0 else 1.0
            
            # 计算相关性
            correlation = np.corrcoef(stock_returns, market_returns)[0, 1]
            
            # 获取行业信息计算集中度
            sector_concentration = await self._get_sector_concentration(symbol)
            
            return MarketRiskMetrics(
                volatility=volatility,
                beta=beta,
                correlation_with_market=correlation,
                liquidity_risk=0.1,  # 简化处理
                sector_concentration=sector_concentration
            )
            
        except Exception as e:
            print(f"❌ 计算市场风险指标失败: {e}")
            return MarketRiskMetrics(
                volatility=0.3,
                beta=1.2,
                correlation_with_market=0.6,
                liquidity_risk=0.2,
                sector_concentration=0.25
            )
    
    async def _get_historical_prices(self, symbol: str, days: int = 60) -> List[float]:
        """获取历史价格数据"""
        try:
            conn = sqlite3.connect(self.realtime_db_path)
            cursor = conn.cursor()
            
            # 获取最近N天的收盘价
            cursor.execute("""
                SELECT price FROM realtime_quotes 
                WHERE symbol = ? 
                ORDER BY timestamp DESC 
                LIMIT ?
            """, (symbol, days))
            
            prices = [row[0] for row in cursor.fetchall()]
            conn.close()
            
            if len(prices) < 10:
                # 如果历史数据不足，生成模拟数据
                base_price = 20.0
                prices = []
                for i in range(days):
                    price = base_price * (1 + np.random.normal(0, 0.02))
                    prices.append(price)
                    base_price = price
            
            return prices[::-1]  # 按时间正序返回
            
        except Exception as e:
            print(f"❌ 获取历史价格失败: {e}")
            return []
    
    def _calculate_portfolio_risk(self, 
                                portfolio_data: Optional[UserPortfolioData],
                                symbol: str,
                                action: str,
                                quantity: int,
                                price: float) -> Dict[str, float]:
        """计算组合风险"""
        if not portfolio_data:
            return {"concentration_risk": 0.1, "sector_risk": 0.1, "total_risk": 0.2}
        
        # 计算新交易后的仓位集中度
        trade_value = quantity * price
        total_assets = portfolio_data.total_assets
        
        if action == "buy":
            new_position_ratio = trade_value / total_assets
        else:
            new_position_ratio = 0  # 卖出降低集中度
        
        # 检查是否超过单股限制
        concentration_risk = min(new_position_ratio / self.risk_config["position_limits"]["single_stock"], 1.0)
        
        # 计算行业集中度风险（简化）
        sector_risk = 0.2  # 默认值
        
        # 计算总体组合风险
        total_risk = (concentration_risk + sector_risk) / 2
        
        return {
            "concentration_risk": concentration_risk,
            "sector_risk": sector_risk,
            "total_risk": total_risk,
            "position_ratio": new_position_ratio
        }
    
    def _calculate_liquidity_risk(self, market_data: RealTimeMarketData) -> float:
        """计算流动性风险"""
        # 基于成交量和换手率评估流动性
        volume = market_data.volume
        amount = market_data.amount
        
        # 简化的流动性评估
        if volume > 10000000:  # 成交量大于1000万
            return 0.1  # 低流动性风险
        elif volume > 1000000:  # 成交量大于100万
            return 0.3  # 中等流动性风险
        else:
            return 0.7  # 高流动性风险
    
    async def _calculate_technical_risk(self, symbol: str) -> float:
        """计算技术面风险"""
        try:
            # 获取技术指标数据
            prices = await self._get_historical_prices(symbol, days=20)
            
            if len(prices) < 10:
                return 0.5  # 默认中等风险
            
            current_price = prices[-1]
            
            # 计算移动平均线
            ma5 = np.mean(prices[-5:]) if len(prices) >= 5 else current_price
            ma10 = np.mean(prices[-10:]) if len(prices) >= 10 else current_price
            ma20 = np.mean(prices[-20:]) if len(prices) >= 20 else current_price
            
            # 技术风险评估
            risk_score = 0.5  # 基础风险
            
            # 价格相对于均线的位置
            if current_price < ma5:
                risk_score += 0.1
            if current_price < ma10:
                risk_score += 0.1
            if current_price < ma20:
                risk_score += 0.1
            
            # 均线排列
            if ma5 < ma10 < ma20:
                risk_score += 0.2  # 空头排列增加风险
            
            return min(risk_score, 1.0)
            
        except Exception as e:
            print(f"❌ 计算技术风险失败: {e}")
            return 0.5
    
    def _calculate_overall_risk(self, 
                              market_risk: MarketRiskMetrics,
                              portfolio_risk: Dict[str, float],
                              liquidity_risk: float,
                              technical_risk: float) -> Any:
        """计算综合风险"""
        
        # 权重配置
        weights = {
            "market": 0.3,
            "portfolio": 0.3,
            "liquidity": 0.2,
            "technical": 0.2
        }
        
        # 计算加权风险分数
        market_score = (market_risk.volatility * 50 + 
                       abs(market_risk.beta - 1) * 30 + 
                       market_risk.sector_concentration * 20)
        
        portfolio_score = portfolio_risk["total_risk"] * 100
        liquidity_score = liquidity_risk * 100
        technical_score = technical_risk * 100
        
        overall_score = (
            market_score * weights["market"] +
            portfolio_score * weights["portfolio"] +
            liquidity_score * weights["liquidity"] +
            technical_score * weights["technical"]
        )
        
        # 确定风险等级
        if overall_score <= 20:
            risk_level = RiskLevel.VERY_LOW
        elif overall_score <= 40:
            risk_level = RiskLevel.LOW
        elif overall_score <= 60:
            risk_level = RiskLevel.MEDIUM
        elif overall_score <= 80:
            risk_level = RiskLevel.HIGH
        else:
            risk_level = RiskLevel.VERY_HIGH
        
        # 风险因子详情
        risk_factors = [
            {
                "factor": "市场风险",
                "score": market_score,
                "description": f"波动率{market_risk.volatility:.2f}, Beta{market_risk.beta:.2f}"
            },
            {
                "factor": "组合风险", 
                "score": portfolio_score,
                "description": f"集中度风险{portfolio_risk['concentration_risk']:.2f}"
            },
            {
                "factor": "流动性风险",
                "score": liquidity_score,
                "description": f"流动性评分{liquidity_risk:.2f}"
            },
            {
                "factor": "技术风险",
                "score": technical_score,
                "description": f"技术面评分{technical_risk:.2f}"
            }
        ]
        
        # 计算置信度
        confidence = 0.8 - (overall_score / 100) * 0.3  # 风险越高置信度越低
        
        return type('OverallRisk', (), {
            'risk_level': risk_level,
            'risk_score': overall_score,
            'risk_factors': risk_factors,
            'confidence': max(confidence, 0.3)
        })()
    
    def _generate_risk_recommendations(self, 
                                     overall_risk: Any,
                                     market_risk: MarketRiskMetrics,
                                     portfolio_risk: Dict[str, float],
                                     action: str) -> List[str]:
        """生成风险建议"""
        recommendations = []
        
        # 基于整体风险等级的建议
        if overall_risk.risk_level in [RiskLevel.HIGH, RiskLevel.VERY_HIGH]:
            recommendations.append("⚠️ 高风险操作，建议谨慎考虑")
            recommendations.append("建议降低交易数量或分批建仓")
            
        if market_risk.volatility > 0.3:
            recommendations.append("股票波动率较高，建议设置较紧的止损")
            
        if portfolio_risk["concentration_risk"] > 0.8:
            recommendations.append("仓位集中度过高，建议分散投资")
            
        if market_risk.beta > 1.5:
            recommendations.append("该股票系统性风险较高，注意大盘走势")
            
        # 基于操作类型的建议
        if action == "buy":
            recommendations.append("买入前请确认技术面支撑位")
        else:
            recommendations.append("卖出时注意市场流动性")
        
        return recommendations
    
    def _calculate_max_position_size(self, 
                                   portfolio_data: Optional[UserPortfolioData],
                                   risk_score: float) -> float:
        """计算最大建议仓位"""
        if not portfolio_data:
            return 0.05  # 默认5%
        
        # 基于风险分数调整最大仓位
        base_limit = self.risk_config["position_limits"]["single_stock"]
        risk_adjustment = 1 - (risk_score / 100) * 0.5  # 风险越高，仓位越小
        
        max_position = base_limit * risk_adjustment
        return max(max_position, 0.02)  # 最小2%
    
    def _calculate_stop_loss_price(self, 
                                 current_price: float,
                                 action: str,
                                 risk_score: float) -> Optional[float]:
        """计算止损价格"""
        if action != "buy":
            return None
        
        # 根据风险分数选择止损比例
        if risk_score <= 40:
            stop_loss_rate = self.risk_config["stop_loss_rates"]["aggressive"]
        elif risk_score <= 70:
            stop_loss_rate = self.risk_config["stop_loss_rates"]["moderate"]
        else:
            stop_loss_rate = self.risk_config["stop_loss_rates"]["conservative"]
        
        return current_price * (1 - stop_loss_rate)
    
    def _calculate_take_profit_price(self, 
                                   current_price: float,
                                   action: str,
                                   risk_score: float) -> Optional[float]:
        """计算止盈价格"""
        if action != "buy":
            return None
        
        # 风险收益比，风险越高要求收益越高
        risk_reward_ratio = 2 + (risk_score / 100)  # 2-3倍风险收益比
        
        if risk_score <= 40:
            take_profit_rate = 0.15  # 15%
        elif risk_score <= 70:
            take_profit_rate = 0.20  # 20%
        else:
            take_profit_rate = 0.25  # 25%
        
        return current_price * (1 + take_profit_rate)
    
    async def _get_sector_concentration(self, symbol: str) -> float:
        """获取行业集中度"""
        try:
            conn = sqlite3.connect(self.db_path)
            cursor = conn.cursor()
            
            cursor.execute("""
                SELECT industry FROM stock_basic_info WHERE symbol = ?
            """, (symbol,))
            
            result = cursor.fetchone()
            conn.close()
            
            if result:
                # 简化的行业集中度计算
                return 0.2  # 默认20%
            else:
                return 0.3  # 未知行业，稍高风险
                
        except Exception as e:
            print(f"❌ 获取行业信息失败: {e}")
            return 0.25
    
    def _create_high_risk_assessment(self, reason: str) -> RiskAssessment:
        """创建高风险评估结果"""
        return RiskAssessment(
            overall_risk_level=RiskLevel.VERY_HIGH,
            risk_score=90.0,
            risk_factors=[{
                "factor": "数据异常",
                "score": 90.0,
                "description": reason
            }],
            recommendations=[f"⚠️ {reason}，建议暂停交易"],
            max_position_size=0.01,
            stop_loss_price=None,
            take_profit_price=None,
            confidence=0.3
        )

# 全局实例
risk_assessment_engine = RiskAssessmentEngine()

# 便捷函数
async def assess_trading_risk(symbol: str, action: str, quantity: int, 
                            price: float, user_id: str) -> RiskAssessment:
    """评估交易风险"""
    return await risk_assessment_engine.assess_trading_risk(
        symbol, action, quantity, price, user_id
    )
