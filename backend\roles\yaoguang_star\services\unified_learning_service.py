#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
瑶光星统一学习服务
合并学习优化、学习环境、个股学习等功能
专注于瑶光星的核心职责：学习和回测
"""

import asyncio
import logging
from datetime import datetime, timedelta
from typing import Dict, Any, List, Optional
from dataclasses import dataclass
from pathlib import Path

logger = logging.getLogger(__name__)

@dataclass
class LearningRecord:
    """学习记录"""
    record_id: str
    learning_type: str
    content: Dict[str, Any]
    performance_impact: float
    confidence: float
    created_time: datetime

@dataclass
class LearningSession:
    """学习会话"""
    session_id: str
    session_type: str
    start_time: datetime
    end_time: Optional[datetime]
    status: str
    results: Dict[str, Any]

class UnifiedLearningService:
    """瑶光星统一学习服务"""
    
    def __init__(self):
        self.service_name = "瑶光星统一学习服务"
        self.version = "1.0.0"
        
        # 学习记录和会话管理
        self.learning_records = {}
        self.learning_sessions = {}
        self.record_counter = 0
        self.session_counter = 0
        
        # 学习配置
        self.learning_config = {
            "learning_rate": 0.1,
            "memory_retention": 0.9,
            "optimization_threshold": 0.05,
            "max_records": 10000,
            "max_concurrent_sessions": 5
        }
        
        # 核心系统集成
        self._init_core_systems()
        
        logger.info(f"✅ {self.service_name} v{self.version} 初始化完成")
    
    def _init_core_systems(self):
        """初始化核心系统"""
        try:
            # 导入真实数据管理器
            from ..core.real_data_manager import real_data_manager
            self.data_manager = real_data_manager
            
            # 导入学习引擎
            from ..core.real_learning_process_engine import real_learning_process_engine
            self.learning_engine = real_learning_process_engine
            
            logger.info("✅ 核心系统集成完成")
            
        except Exception as e:
            logger.warning(f"核心系统集成失败: {e}")
            self.data_manager = None
            self.learning_engine = None
    
    async def create_learning_session(self, session_type: str, config: Dict[str, Any] = None) -> Dict[str, Any]:
        """创建学习会话"""
        try:
            if len(self.learning_sessions) >= self.learning_config["max_concurrent_sessions"]:
                return {
                    "success": False,
                    "error": "已达到最大并发学习会话数量"
                }
            
            session_id = f"learning_session_{datetime.now().strftime('%Y%m%d_%H%M%S')}_{self.session_counter}"
            self.session_counter += 1
            
            session = LearningSession(
                session_id=session_id,
                session_type=session_type,
                start_time=datetime.now(),
                end_time=None,
                status="active",
                results={}
            )
            
            self.learning_sessions[session_id] = session
            
            # 启动学习流程
            if self.learning_engine:
                learning_result = await self.learning_engine.start_learning_cycle(config or {})
                session.results["learning_cycle"] = learning_result
            
            logger.info(f"🎯 创建学习会话: {session_id}")
            
            return {
                "success": True,
                "session_id": session_id,
                "session_type": session_type,
                "start_time": session.start_time.isoformat()
            }
            
        except Exception as e:
            logger.error(f"创建学习会话失败: {e}")
            return {
                "success": False,
                "error": str(e)
            }
    
    async def execute_stock_learning(self, stock_code: str, learning_type: str = "comprehensive") -> Dict[str, Any]:
        """执行个股学习"""
        try:
            logger.info(f"🔍 开始个股学习: {stock_code}")
            
            if not self.data_manager:
                return {
                    "success": False,
                    "error": "数据管理器不可用"
                }
            
            # 获取股票数据
            end_date = datetime.now().strftime('%Y-%m-%d')
            start_date = (datetime.now() - timedelta(days=365)).strftime('%Y-%m-%d')
            
            stock_data = await self.data_manager.get_real_stock_data(stock_code, start_date, end_date)
            
            if stock_data.empty:
                return {
                    "success": False,
                    "error": f"无法获取股票 {stock_code} 的数据"
                }
            
            # 获取技术指标
            tech_indicators = await self.data_manager.get_real_technical_indicators(stock_code, start_date, end_date)
            
            # 执行学习分析
            learning_results = {
                "stock_code": stock_code,
                "learning_type": learning_type,
                "data_points": len(stock_data),
                "indicators_count": len(tech_indicators.columns) if not tech_indicators.empty else 0,
                "analysis_period": f"{start_date} 到 {end_date}",
                "learning_insights": []
            }
            
            # 基于数据进行学习分析
            if not stock_data.empty:
                # 价格趋势分析
                price_trend = self._analyze_price_trend(stock_data)
                learning_results["learning_insights"].append(f"价格趋势: {price_trend}")
                
                # 波动性分析
                volatility = self._analyze_volatility(stock_data)
                learning_results["learning_insights"].append(f"波动性: {volatility}")
                
                # 技术指标学习
                if not tech_indicators.empty:
                    tech_insights = self._analyze_technical_indicators(tech_indicators)
                    learning_results["learning_insights"].extend(tech_insights)
            
            # 记录学习结果
            await self._record_learning_result(stock_code, learning_results)
            
            logger.info(f"✅ 完成个股学习: {stock_code}")
            
            return {
                "success": True,
                "learning_results": learning_results
            }
            
        except Exception as e:
            logger.error(f"个股学习失败 {stock_code}: {e}")
            return {
                "success": False,
                "error": str(e)
            }
    
    def _analyze_price_trend(self, stock_data) -> str:
        """分析价格趋势"""
        try:
            if len(stock_data) < 2:
                return "数据不足"
            
            first_price = stock_data['close_price'].iloc[0]
            last_price = stock_data['close_price'].iloc[-1]
            
            change_percent = (last_price - first_price) / first_price * 100
            
            if change_percent > 10:
                return "强上涨趋势"
            elif change_percent > 0:
                return "上涨趋势"
            elif change_percent > -10:
                return "下跌趋势"
            else:
                return "强下跌趋势"
                
        except Exception:
            return "趋势分析失败"
    
    def _analyze_volatility(self, stock_data) -> str:
        """分析波动性"""
        try:
            if len(stock_data) < 2:
                return "数据不足"
            
            returns = stock_data['close_price'].pct_change().dropna()
            volatility = returns.std()
            
            if volatility > 0.03:
                return "高波动"
            elif volatility > 0.015:
                return "中等波动"
            else:
                return "低波动"
                
        except Exception:
            return "波动性分析失败"
    
    def _analyze_technical_indicators(self, tech_indicators) -> List[str]:
        """分析技术指标"""
        insights = []
        
        try:
            if 'ta_rsi' in tech_indicators.columns:
                latest_rsi = tech_indicators['ta_rsi'].iloc[-1]
                if latest_rsi > 70:
                    insights.append("RSI显示超买状态")
                elif latest_rsi < 30:
                    insights.append("RSI显示超卖状态")
                else:
                    insights.append("RSI处于正常范围")
            
            if 'ta_macd_line' in tech_indicators.columns:
                latest_macd = tech_indicators['ta_macd_line'].iloc[-1]
                if abs(latest_macd) > 0.1:
                    insights.append("MACD显示强趋势信号")
                else:
                    insights.append("MACD显示弱趋势信号")
            
        except Exception as e:
            insights.append(f"技术指标分析失败: {e}")
        
        return insights
    
    async def _record_learning_result(self, stock_code: str, results: Dict[str, Any]):
        """记录学习结果"""
        try:
            record_id = f"learning_record_{datetime.now().strftime('%Y%m%d_%H%M%S')}_{self.record_counter}"
            self.record_counter += 1
            
            record = LearningRecord(
                record_id=record_id,
                learning_type="stock_analysis",
                content={
                    "stock_code": stock_code,
                    "results": results
                },
                performance_impact=0.8,  # 基于结果质量评估
                confidence=0.9,
                created_time=datetime.now()
            )
            
            self.learning_records[record_id] = record
            
            # 限制记录数量
            if len(self.learning_records) > self.learning_config["max_records"]:
                oldest_record = min(self.learning_records.keys(), 
                                  key=lambda x: self.learning_records[x].created_time)
                del self.learning_records[oldest_record]
            
        except Exception as e:
            logger.warning(f"记录学习结果失败: {e}")
    
    async def get_learning_status(self) -> Dict[str, Any]:
        """获取学习状态"""
        active_sessions = [s for s in self.learning_sessions.values() if s.status == "active"]
        
        return {
            "service_name": self.service_name,
            "version": self.version,
            "active_sessions": len(active_sessions),
            "total_records": len(self.learning_records),
            "learning_config": self.learning_config,
            "core_systems_available": self.data_manager is not None and self.learning_engine is not None,
            "last_update": datetime.now().isoformat()
        }
    
    async def optimize_learning_parameters(self) -> Dict[str, Any]:
        """优化学习参数"""
        try:
            # 基于历史学习记录优化参数
            if not self.learning_records:
                return {
                    "success": False,
                    "message": "没有足够的学习记录进行优化"
                }
            
            # 计算平均性能影响
            avg_performance = sum(r.performance_impact for r in self.learning_records.values()) / len(self.learning_records)
            
            # 根据性能调整学习率
            if avg_performance > 0.8:
                self.learning_config["learning_rate"] = min(0.2, self.learning_config["learning_rate"] * 1.1)
            elif avg_performance < 0.6:
                self.learning_config["learning_rate"] = max(0.05, self.learning_config["learning_rate"] * 0.9)
            
            logger.info(f"🔧 学习参数优化完成，平均性能: {avg_performance:.3f}")
            
            return {
                "success": True,
                "avg_performance": avg_performance,
                "optimized_config": self.learning_config
            }
            
        except Exception as e:
            logger.error(f"学习参数优化失败: {e}")
            return {
                "success": False,
                "error": str(e)
            }

# 全局统一学习服务实例
unified_learning_service = UnifiedLearningService()
