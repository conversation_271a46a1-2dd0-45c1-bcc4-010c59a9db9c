#!/usr/bin/env python3
"""
测试数据库中的真实股票数据
"""

import sys
import asyncio
import sqlite3
import os
sys.path.append('.')

async def test_database_stocks():
    """测试数据库中的真实股票数据"""
    try:
        print('🔍' + '='*60)
        print('🔍 测试数据库中的真实股票数据')
        print('🔍' + '='*60)
        
        # 测试统一系统的数据库获取功能
        from roles.yaoguang_star.core.unified_yaoguang_system import unified_yaoguang_system
        
        print('\n📊 测试统一系统的数据库股票获取...')
        real_stocks = await unified_yaoguang_system._get_real_stocks_from_database()
        
        if real_stocks:
            print(f'✅ 成功从数据库获取 {len(real_stocks)} 只真实股票:')
            for i, stock in enumerate(real_stocks, 1):
                print(f'   {i}. {stock}')
        else:
            print('❌ 未能从数据库获取任何股票')
        
        # 直接测试数据库连接
        print('\n🗄️ 直接测试数据库连接...')
        
        db_paths = [
            "data/stock_master.db",
            "data/stock_historical.db", 
            "data/stock_realtime.db"
        ]
        
        all_stocks = set()
        
        for db_path in db_paths:
            if os.path.exists(db_path):
                try:
                    conn = sqlite3.connect(db_path)
                    cursor = conn.cursor()
                    
                    print(f'\n✅ 连接数据库: {db_path}')
                    
                    # 查询股票代码
                    queries = [
                        "SELECT DISTINCT stock_code FROM stock_info WHERE stock_code IS NOT NULL LIMIT 10",
                        "SELECT DISTINCT stock_code FROM enhanced_fundamental_data WHERE stock_code IS NOT NULL LIMIT 10",
                        "SELECT DISTINCT stock_code FROM industry_classification WHERE stock_code IS NOT NULL LIMIT 10"
                    ]
                    
                    for query in queries:
                        try:
                            cursor.execute(query)
                            results = cursor.fetchall()
                            
                            if results:
                                print(f'   查询成功: {len(results)} 条记录')
                                for row in results[:5]:  # 只显示前5个
                                    if row[0] and isinstance(row[0], str):
                                        stock_code = row[0].strip()
                                        if len(stock_code) == 6 and stock_code.isdigit():
                                            all_stocks.add(stock_code)
                                            print(f'     - {stock_code}')
                                break
                        except sqlite3.Error as e:
                            print(f'   查询失败: {e}')
                            continue
                    
                    conn.close()
                    
                except Exception as e:
                    print(f'❌ 数据库连接失败: {e}')
        
        print(f'\n📈 总共发现 {len(all_stocks)} 只不同的股票')
        if all_stocks:
            sample_stocks = list(all_stocks)[:10]
            print('   样本股票:')
            for i, stock in enumerate(sample_stocks, 1):
                print(f'     {i}. {stock}')
        
        # 测试统一系统的学习会话
        print('\n🧠 测试统一系统学习会话...')
        
        learning_config = {
            "session_id": "database_test_001",
            "learning_mode": "stock_analysis",
            "target_stocks": 2,
            "requester": "database_test"
        }
        
        learning_result = await unified_yaoguang_system.start_learning_session(learning_config)
        
        if learning_result.get('success'):
            session_id = learning_result.get('session_id')
            print(f'✅ 学习会话启动成功: {session_id}')
            
            # 等待学习完成
            await asyncio.sleep(3)
            
            # 获取学习状态
            status_result = await unified_yaoguang_system.get_learning_status(session_id)
            
            if status_result and status_result.get('success'):
                learning_results = status_result.get('learning_results', {})
                print(f'✅ 学习状态获取成功')
                
                # 检查选股结果
                if 'selected_stocks' in learning_results:
                    selected_stocks = learning_results['selected_stocks']
                    print(f'📊 选择的股票: {selected_stocks}')
                    
                    if selected_stocks and len(selected_stocks) > 0:
                        print('✅ 成功获取真实选股结果！')
                        
                        # 验证股票代码格式
                        valid_stocks = []
                        for stock in selected_stocks:
                            if isinstance(stock, str) and len(stock) == 6 and stock.isdigit():
                                valid_stocks.append(stock)
                        
                        if valid_stocks:
                            print(f'✅ 验证通过: {len(valid_stocks)} 只股票格式正确')
                        else:
                            print('❌ 警告: 股票代码格式不正确')
                    else:
                        print('❌ 选股结果为空')
                else:
                    print('❌ 学习结果中没有选股数据')
            else:
                print('❌ 学习状态获取失败')
        else:
            print(f'❌ 学习会话启动失败: {learning_result.get("error")}')
        
        print('\n' + '='*60)
        print('🎯 数据库股票测试完成')
        print('='*60)
        
    except Exception as e:
        print(f'❌ 测试失败: {e}')
        import traceback
        traceback.print_exc()

if __name__ == "__main__":
    asyncio.run(test_database_stocks())
