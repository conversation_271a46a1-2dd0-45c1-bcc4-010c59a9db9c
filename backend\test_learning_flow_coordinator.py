#!/usr/bin/env python3
"""
学习流程协调器测试脚本
"""

import sys
import asyncio
sys.path.append('.')

async def test_learning_coordinator():
    """测试学习流程协调器"""
    try:
        from roles.yaoguang_star.core.learning_flow_coordinator import learning_flow_coordinator
        
        print('🎓 测试学习流程协调器...')
        print(f'协调器名称: {learning_flow_coordinator.coordinator_name}')
        print(f'版本: {learning_flow_coordinator.version}')
        
        # 显示星际学习能力
        print('\n🌟 星际学习能力映射:')
        for star, capabilities in learning_flow_coordinator.star_learning_capabilities.items():
            print(f'  {star}: {len(capabilities["specialties"])} 个专长')
            print(f'    专长: {capabilities["specialties"][:2]}...')
            print(f'    学习类型: {capabilities["learning_types"]}')
        
        # 测试综合学习流程
        learning_config = {
            'learning_focus': ['pattern_recognition', 'risk_assessment', 'strategy_optimization'],
            'learning_mode': 'comprehensive',
            'data_range': {
                'start_date': '2023-01-01',
                'end_date': '2024-12-31'
            },
            'objectives': {
                'accuracy_target': 0.85,
                'risk_control': 0.10,
                'return_target': 0.15
            }
        }
        
        print('\n🚀 启动综合学习流程...')
        result = await learning_flow_coordinator.start_comprehensive_learning(learning_config)
        
        if result.get('success'):
            print('✅ 综合学习成功！')
            print(f'会话ID: {result["session_id"]}')
            print(f'学习目标数: {result["objectives_count"]}')
            
            learning_result = result['learning_result']
            if learning_result.get('success'):
                print(f'总耗时: {learning_result["total_duration"]}')
                print(f'目标达成数: {learning_result["objectives_achieved"]}')
                print(f'知识获得量: {learning_result["knowledge_gained"]}')
                
                # 显示各阶段结果
                print('\n📊 各学习阶段结果:')
                learning_results = learning_result['learning_results']
                for stage, stage_result in learning_results.items():
                    status = '✅' if stage_result.get('success') else '❌'
                    print(f'  {status} {stage}: {stage_result.get("success", False)}')
                    
                    # 显示详细信息
                    if stage == 'analysis' and stage_result.get('success'):
                        print(f'    完成星数: {stage_result.get("stars_completed", 0)}')
                        print(f'学习模式数: {stage_result.get("total_patterns_learned", 0)}')
                        print(f'知识库更新: {stage_result.get("knowledge_base_updated", 0)}')
                    
                    elif stage == 'strategy_learning' and stage_result.get('success'):
                        print(f'    策略优化: {stage_result.get("strategies_optimized", 0)}')
                        print(f'执行算法: {stage_result.get("execution_algorithms_learned", 0)}')
                
                # 显示性能摘要
                print('\n📈 学习性能摘要:')
                perf_summary = learning_result.get('performance_summary', {})
                print(f'  整体进度: {perf_summary.get("overall_progress", 0):.1%}')
                
                stage_completion = perf_summary.get('stage_completion', {})
                completed_stages = len([s for s in stage_completion.values() if s])
                total_stages = len(stage_completion)
                print(f'  阶段完成率: {completed_stages}/{total_stages} ({completed_stages/max(1,total_stages):.1%})')
                
            else:
                print(f'学习执行失败: {learning_result.get("error")}')
        else:
            print(f'❌ 综合学习失败: {result.get("error")}')
        
        # 显示活跃会话状态
        print(f'\n📋 活跃学习会话数: {len(learning_flow_coordinator.active_learning_sessions)}')
        print(f'已完成学习会话数: {len(learning_flow_coordinator.completed_learning_sessions)}')
        
        # 测试学习能力查询
        print('\n🔍 测试学习能力查询:')
        for star_name in ['kaiyang', 'tianshu', 'tianji']:
            capabilities = learning_flow_coordinator.star_learning_capabilities.get(star_name, {})
            specialties = capabilities.get('specialties', [])
            print(f'  {star_name}: {len(specialties)} 个专长 - {specialties[0] if specialties else "无"}')
            
    except Exception as e:
        print(f'❌ 测试执行失败: {e}')
        import traceback
        traceback.print_exc()

if __name__ == "__main__":
    asyncio.run(test_learning_coordinator())
