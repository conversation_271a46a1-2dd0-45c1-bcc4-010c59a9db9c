#!/usr/bin/env python3
"""
测试瑶光星真实业务流程
验证：开阳选股 → 三星分析 → 天权决策 → 玉衡执行
"""

import sys
import asyncio
sys.path.append('.')

async def test_real_business_flow():
    """测试真实业务流程"""
    try:
        print('🌟' + '='*80)
        print('🌟 测试瑶光星真实业务流程')
        print('🌟 流程：开阳选股 → 三星分析 → 天权决策 → 玉衡执行')
        print('🌟' + '='*80)
        
        from roles.yaoguang_star.yaoguang_coordination_service import yaoguang_coordination_service
        
        # 测试1：真实学习流程
        print('\n🎓 测试1：真实学习业务流程...')
        learning_config = {
            "learning_focus": ["pattern_recognition", "risk_assessment"],
            "learning_mode": "comprehensive",
            "use_real_business_flow": True,  # 使用真实业务流程
            "target_stocks": 2,
            "priority": "high",
            "requester": "real_business_test"
        }
        
        print(f'   配置: 使用真实业务流程 = {learning_config["use_real_business_flow"]}')
        print(f'   目标股票数: {learning_config["target_stocks"]}')
        print(f'   学习重点: {learning_config["learning_focus"]}')
        
        learning_result = await yaoguang_coordination_service.coordinate_learning_session(learning_config)
        
        if learning_result.get('success'):
            print('✅ 真实学习流程成功！')
            print(f'   请求ID: {learning_result["request_id"]}')
            print(f'   执行时间: {learning_result["execution_time"]:.2f}秒')
            print(f'   使用协调器: {learning_result["coordinator_used"]}')
            
            coord_result = learning_result['coordination_result']
            if coord_result.get('success'):
                flow_result = coord_result.get('flow_result', {})
                if flow_result.get('success'):
                    print(f'\n📊 真实业务流程结果:')
                    print(f'   协调类型: {flow_result.get("coordination_type")}')
                    print(f'   完成步骤数: {flow_result.get("steps_completed", 0)}')
                    print(f'   选择股票数: {flow_result.get("stocks_selected", 0)}')
                    print(f'   决策数量: {flow_result.get("decisions_made", 0)}')
                    print(f'   执行数量: {flow_result.get("executions_completed", 0)}')
                    print(f'   协调效果: {flow_result.get("coordination_effectiveness", 0):.2f}')
                    
                    # 显示选择的股票
                    selected_stocks = coord_result.get('selected_stocks', [])
                    if selected_stocks:
                        print(f'\n🎯 开阳星选择的股票:')
                        for i, stock in enumerate(selected_stocks[:3], 1):
                            print(f'     {i}. {stock}')
                    
                    # 显示决策结果
                    final_decisions = coord_result.get('final_decisions', [])
                    if final_decisions:
                        print(f'\n👑 天权星决策结果: {len(final_decisions)} 个决策')
                    
                    # 显示执行结果
                    execution_results = coord_result.get('execution_results', [])
                    if execution_results:
                        print(f'\n⚡ 玉衡星执行结果: {len(execution_results)} 个执行')
                    
                    # 检查是否有真实数据
                    detailed_results = flow_result.get("detailed_results", {})
                    print(f'\n🔍 详细结果分析:')
                    for step, result in detailed_results.items():
                        if isinstance(result, dict):
                            success = result.get("success", False)
                            status = '✅' if success else '❌'
                            print(f'   {status} {step}: {success}')
                            
                            # 检查是否有真实数据
                            if step == "kaiyang_selection":
                                method = result.get("selection_method", "unknown")
                                print(f'       选择方法: {method}')
                            elif step == "three_stars_analysis":
                                if result.get("data_source"):
                                    print(f'       数据源: {result.get("data_source")}')
                            elif step == "tianquan_decision":
                                if result.get("decision_type"):
                                    print(f'       决策类型: {result.get("decision_type")}')
                else:
                    print(f'❌ 业务流程执行失败: {flow_result.get("error")}')
            else:
                print(f'❌ 协调结果失败: {coord_result.get("error")}')
        else:
            print(f'❌ 真实学习流程失败: {learning_result.get("error")}')
        
        # 测试2：真实回测流程
        print('\n🔄 测试2：真实回测业务流程...')
        backtest_config = {
            "strategy_name": "七星真实协调策略",
            "start_date": "2024-01-01",
            "end_date": "2024-03-31",
            "initial_capital": 1000000.0,
            "use_real_business_flow": True,  # 使用真实业务流程
            "priority": "high",
            "requester": "real_business_test"
        }
        
        print(f'   策略: {backtest_config["strategy_name"]}')
        print(f'   期间: {backtest_config["start_date"]} 至 {backtest_config["end_date"]}')
        print(f'   使用真实流程: {backtest_config["use_real_business_flow"]}')
        
        backtest_result = await yaoguang_coordination_service.coordinate_backtest_session(backtest_config)
        
        if backtest_result.get('success'):
            print('✅ 真实回测流程成功！')
            print(f'   请求ID: {backtest_result["request_id"]}')
            print(f'   执行时间: {backtest_result["execution_time"]:.2f}秒')
            print(f'   使用协调器: {backtest_result["coordinator_used"]}')
            
            coord_result = backtest_result['coordination_result']
            if coord_result.get('success'):
                backtest_res = coord_result.get('backtest_result', {})
                if backtest_res.get('success'):
                    print(f'\n📊 真实回测结果:')
                    print(f'   协调类型: {backtest_res.get("coordination_type")}')
                    print(f'   回测期间: {backtest_res.get("backtest_period")}')
                    print(f'   数据源: {backtest_res.get("data_source")}')
                    
                    # 显示策略绩效
                    strategy_performance = backtest_res.get("strategy_performance", {})
                    if strategy_performance:
                        print(f'\n📈 策略绩效指标:')
                        print(f'   总收益率: {strategy_performance.get("total_return", 0):.1%}')
                        print(f'   夏普比率: {strategy_performance.get("sharpe_ratio", 0):.2f}')
                        print(f'   最大回撤: {strategy_performance.get("max_drawdown", 0):.1%}')
                        print(f'   胜率: {strategy_performance.get("win_rate", 0):.1%}')
                        print(f'   数据源: {strategy_performance.get("data_source", "unknown")}')
                        print(f'   计算方法: {strategy_performance.get("calculation_method", "unknown")}')
                else:
                    print(f'❌ 回测执行失败: {backtest_res.get("error")}')
            else:
                print(f'❌ 回测协调失败: {coord_result.get("error")}')
        else:
            print(f'❌ 真实回测流程失败: {backtest_result.get("error")}')
        
        # 测试3：检查真实业务协调器状态
        print('\n🔧 测试3：检查真实业务协调器状态...')
        try:
            real_coordinator = yaoguang_coordination_service.real_business_coordinator
            print(f'✅ 真实业务协调器: {real_coordinator.coordinator_name} v{real_coordinator.version}')
            print(f'   活跃会话: {len(real_coordinator.active_sessions)}')
            print(f'   已完成会话: {len(real_coordinator.completed_sessions)}')
            
            # 检查统一系统连接
            if real_coordinator.unified_system:
                print(f'   统一系统: 已连接')
            else:
                print(f'   统一系统: 未连接')
                
        except Exception as e:
            print(f'❌ 真实业务协调器检查失败: {e}')
        
        # 获取最终协调状态
        print('\n📈 最终协调状态:')
        final_status = await yaoguang_coordination_service.get_coordination_status()
        
        session_stats = final_status.get("session_statistics", {})
        print(f'   学习会话: {session_stats.get("learning", 0)}')
        print(f'   回测会话: {session_stats.get("backtest", 0)}')
        
        perf_metrics = final_status.get("performance_metrics", {})
        print(f'   总协调数: {perf_metrics.get("total_coordinations", 0)}')
        print(f'   成功协调数: {perf_metrics.get("successful_coordinations", 0)}')
        
        # 检查引擎状态
        engine_status = final_status.get("engine_status", {})
        print(f'\n🔧 协调引擎状态:')
        for engine, status in engine_status.items():
            print(f'   {engine}: {status}')
        
        print('\n' + '='*80)
        print('🎉 瑶光星真实业务流程测试完成！')
        print('🎉 验证了开阳选股 → 三星分析 → 天权决策 → 玉衡执行的完整流程！')
        print('='*80)
        
    except Exception as e:
        print(f'❌ 测试执行失败: {e}')
        import traceback
        traceback.print_exc()

if __name__ == "__main__":
    asyncio.run(test_real_business_flow())
